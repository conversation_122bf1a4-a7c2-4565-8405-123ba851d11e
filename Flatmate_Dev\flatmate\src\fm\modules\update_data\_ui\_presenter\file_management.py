"""
File management for Update Data module.

This module consolidates source and archive management logic including:
- File and folder selection
- Save location selection and "same as source" logic
- Folder monitoring
- File discovery and enrichment
- Source and save option changes

Consolidated from SourceManager and ArchiveManager as part of the consolidation refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING, List

from fm.core.services.logger import log
from fm.modules.update_data._ui.option_types import SourceOptions, SaveOptions
from fm.modules.update_data.services.file_info_service import FileInfoService
from fm.modules.update_data._ui.ui_events import SourceDiscoveredEvent, FileDisplayUpdateEvent
from fm.modules.update_data.services.local_event_bus import ViewEvents
from ...config.ud_keys import UpdateDataKeys as ud_keys
from ...config.ud_config import ud_config
from .._view.shared_components.file_selector import FileSelector, FileUtils


if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_coordinator import UpdateDataState, StateManager


class FileManager:
    """
    Manages all file/folder selection and save location logic.

    This class consolidates:
    - File and folder selection dialogs
    - File enrichment using FileInfoService
    - Folder monitoring integration
    - Save location selection and "same as source" functionality
    - Source and save option changes
    """

    def __init__(self, view: 'IUpdateDataView', state_manager: 'StateManager',
                 file_list_manager, local_bus):
        """
        Initialize the file manager.

        Args:
            view: The view interface
            state_manager: Consolidated state and UI sync manager
            file_list_manager: FileListManager for canonical file list operations
            local_bus: Local event bus for module events
        """
        self.view = view
        self.state_manager = state_manager  # Consolidated state + widget state manager
        self.state = state_manager.state  # Direct access to state data
        self.file_list_manager = file_list_manager  # NEW: Delegate file list operations
        self.local_bus = local_bus

        # File info service for enriching file data
        self.file_info_service = FileInfoService()
        
        # Track selected source for "same as source" functionality
        self.selected_source = None
        
        # REMOVED: self.file_paths_list - now owned by FileListManager
        log.debug("[FILE_MANAGER] Initialized with FileListManager delegation")

        # Subscribe distinct add-files channel to avoid SOURCE_SELECT_REQUESTED collision
        self.subscribe_add_files_channel()

    # =============================================================================
    # SOURCE SELECTION METHODS (from SourceManager)
    # =============================================================================
    
    def handle_source_select(self, selection_type):
        """
        Handle source selection request from the view.

        Args:
            selection_type: Type of selection (option string from UI)
        """
        log.debug(f"Source selection requested: {selection_type}")

        # Enhanced mapping for UI strings to internal types
        if (selection_type == 'files' or
            selection_type == SourceOptions.SELECT_FILES or
            selection_type == "Select individual files..."):
            self._select_files()
        elif (selection_type == 'folder' or
              selection_type == SourceOptions.SELECT_FOLDER or
              selection_type == "Select entire folder..."):
            self._select_folder()
        else:
            log.warning(f"Unknown selection type: {selection_type}")
            return

        # After source selection, update save location if "same as source"
        self._update_save_location_for_source()

        # Update UI state
        self.state_manager.sync_state_to_view()

    def subscribe_add_files_channel(self):
        """
        Subscribe to a distinct 'add_files_requested' channel originating from UDFileView.
        This avoids collisions with Left Panel SOURCE_SELECT_REQUESTED.
        """
        try:
            self.local_bus.subscribe("add_files_requested", self._on_add_files_requested)
            log.debug("[FILE_MANAGER] Subscribed to add_files_requested channel")
        except Exception as e:
            log.error(f"[FILE_MANAGER] Error subscribing to add_files_requested: {e}")

    def _on_add_files_requested(self, _payload=None):
        """Handle UDFileView distinct add-files intent by opening files dialog only."""
        try:
            log.debug("[FILE_MANAGER] Handling add_files_requested → opening files dialog")
            self._select_files()
            # Save location/state sync handled inside _select_files/_process_selected_files
        except Exception as e:
            log.error(f"[FILE_MANAGER] Error handling add_files_requested: {e}")

    def _select_files(self):
        """Select individual files using unified file selector API."""
        try:
            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))

            file_paths = FileSelector.get_paths(
                selection_type='files',
                initial_dir=last_dir,
                title="Select CSV Files to Process",
                parent=self.view
            )

            if file_paths:
                self._process_selected_files(file_paths, 'files')

        except Exception as e:
            log.error(f"[FILE_MANAGER] Error selecting files: {e}")
            # Emit dialog-request event instead of calling view.show_error directly
            try:
                from ..ui_events import DialogRequestEvent
                self.local_bus.emit(
                    ViewEvents.ERROR_DIALOG_REQUESTED.value,
                    DialogRequestEvent(
                        dialog_type="error",
                        title="File Selection Error",
                        extra_data={"message": f"Error selecting files: {str(e)}"}
                    )
                )
            except Exception as emit_err:
                log.error(f"[FILE_MANAGER] Failed to emit error dialog request: {emit_err}")

    def _select_folder(self):
        """Select folder using unified file selector API."""
        try:
            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))

            file_paths = FileSelector.get_paths(
                selection_type='folder',
                initial_dir=last_dir,
                title="Select Folder Containing CSV Files",
                parent=self.view
            )

            if file_paths:
                # Extract folder path from first file for save location logic
                folder_path = os.path.dirname(file_paths[0])
                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                self._process_selected_files(file_paths, 'folder', folder_path)
            else:
                # Handle case where folder was selected but no files found
                log.warning("[FILE_MANAGER] No supported files found in selected folder")
                # Emit dialog-request for no files found
                try:
                    from ..ui_events import DialogRequestEvent
                    self.local_bus.emit(
                        ViewEvents.ERROR_DIALOG_REQUESTED.value,
                        DialogRequestEvent(
                            dialog_type="error",
                            title="No Files Found",
                            extra_data={"message": "No supported files found in the selected folder."}
                        )
                    )
                except Exception as emit_err:
                    log.error(f"[FILE_MANAGER] Failed to emit 'no files found' dialog request: {emit_err}")

        except Exception as e:
            log.error(f"[FILE_MANAGER] Error selecting folder: {e}")
            # Emit dialog-request event instead of calling view.show_error directly
            try:
                from ..ui_events import DialogRequestEvent
                self.local_bus.emit(
                    ViewEvents.ERROR_DIALOG_REQUESTED.value,
                    DialogRequestEvent(
                        dialog_type="error",
                        title="Folder Selection Error",
                        extra_data={"message": f"Error selecting folder: {str(e)}"}
                    )
                )
            except Exception as emit_err:
                log.error(f"[FILE_MANAGER] Failed to emit error dialog request: {emit_err}")

    def _process_selected_files(self, file_paths: List[str], source_type: str, source_path: str = None):
        """Common method to process selected files regardless of selection method."""
        log.debug(f"[FILE_MANAGER] Processing {len(file_paths)} files from {source_type}")

        # Store selected source for "same as source" functionality
        self.selected_source = source_path or file_paths
        self.state.source_type = source_type

        # Delegate to FileListManager
        self.file_list_manager.set_files(file_paths, source_path or "")

        # Update state
        if source_type == 'files':
            self.state.selected_files = file_paths
        else:
            self.state.selected_folder = source_path
            self.state.selected_files = file_paths

        # Enrich and display file info
        enriched_info = self.enrich_file_info(file_paths)
        self.view.display_enriched_file_info(enriched_info)

        # Update can_process flag
        self.state.update_can_process()

        # Emit event for file discovery
        source_data = SourceDiscoveredEvent(
            source_type=source_type,
            files=file_paths,
            path=source_path or (file_paths[0] if file_paths else ''),
            count=len(file_paths)
        )
        self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value, source_data)

    def enrich_file_info(self, file_paths):
        """
        Enrich file paths with additional metadata.

        Args:
            file_paths: List of file paths to enrich

        Returns:
            List of enriched file info dictionaries
        """
        try:
            return self.file_info_service.discover_files(file_paths)
        except Exception as e:
            log.error(f"Error enriching file info: {e}")
            return [{'path': path, 'name': os.path.basename(path)} for path in file_paths]

    # REMOVED: handle_folder_monitor_file_discovered - now handled by FileListManager
    # REMOVED: toggle_folder_monitoring - now handled by FileListManager
    # REMOVED: handle_monitor_folder_change - now handled by FileListManager

    def handle_source_option_change(self, option):
        """
        Handle source option changes.

        Args:
            option: The new source option value
        """
        try:
            log.debug(f"Source option changed to: {option}")
            self.state.source_option = option

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling source option change: {e}")

    # =============================================================================
    # SAVE LOCATION METHODS (from ArchiveManager)
    # =============================================================================

    def handle_save_select(self):
        """Handle save location selection request from the view."""
        try:
            save_path = self.view.show_folder_dialog()
            if save_path:
                log.debug(f"Save location selected: {save_path}")
                self.state.save_location = save_path
                self.state.update_can_process()
                
                # Update UI state
                self.state_manager.sync_state_to_view()
                
        except Exception as e:
            log.error(f"Error selecting save location: {e}")

    def handle_save_option_change(self, option):
        """
        Handle save option changes.

        Args:
            option: The new save option value
        """
        try:
            log.debug(f"Save option changed to: {option}")
            self.state.save_option = option

            # Update save location based on option
            self._update_save_location_for_source()

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling save option change: {e}")

    # =============================================================================
    # INTERNAL CONSOLIDATION METHODS
    # =============================================================================

    def _update_save_location_for_source(self):
        """
        Handle 'same as source' logic internally.
        
        This method consolidates the cross-manager communication that was
        previously handled by method wrapping in the presenter.
        """
        try:
            if (self.state.save_option == SaveOptions.SAME_AS_SOURCE and 
                self.selected_source is not None):
                
                if self.state.source_type == 'folder':
                    # For folders, use the folder path directly
                    self.state.save_location = self.selected_source
                    log.debug(f"Save location set to source folder: {self.selected_source}")
                    
                elif self.state.source_type == 'files' and self.selected_source:
                    # For files, use the directory of the first file
                    first_file_path = self.selected_source[0] if isinstance(self.selected_source, list) else self.selected_source
                    save_dir = os.path.dirname(first_file_path)
                    self.state.save_location = save_dir
                    log.debug(f"Save location set to source directory: {save_dir}")
                    
                self.state.update_can_process()
                
        except Exception as e:
            log.error(f"Error updating save location for source: {e}")
