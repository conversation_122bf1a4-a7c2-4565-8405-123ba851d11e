# CONCRETE IMPLEMENTATION DECISIONS
## File Pane Architecture - No BS Implementation Guide

**Date**: August 2, 2025
**Focus**: Specific implementation decisions, not architectural theory

---

## DECISION 1: FOLDER STRUCTURE (FINAL)

```
update_data/
├── ud_presenter.py              # TOP LEVEL - module coordinator/API
├── ui/                          # UI-specific code only
│   ├── view/
│   │   ├── ud_view.py
│   │   ├── interface/
│   │   │   └── i_view_interface.py    # METHODS ONLY
│   │   └── components/
│   │       └── file_pane/
│   │           └── ud_file_view.py
│   ├── events/                  # SEPARATE FROM INTERFACE
│   │   ├── __init__.py
│   │   └── file_events.py
│   └── managers/                # If keeping managers
├── services/                    # Business logic
└── config/                      # Module config
```

**RATIONALE**: ud_presenter is the module's main API - it stays at top level. UI concerns go in ui/ folder.

---

## DECISION 2: EVENTS vs INTERFACE (FINAL ANSWER)

**KEEP THEM SEPARATE** - You're right, floating signals in Protocol makes no sense.

### Interface (Methods Only)
```python
# ui/view/interface/i_view_interface.py
class IUpdateDataView(Protocol):
    def add_files(self, files: List[str]) -> None: ...
    def remove_file(self, file_path: str) -> None: ...
    def get_current_files(self) -> List[str]: ...
    def set_processing_state(self, processing: bool) -> None: ...
    # NO SIGNALS HERE - they can't exist in Protocol
```

### Events (Separate Import)
```python
# ui/events/file_events.py
from PySide6.QtCore import Signal, QObject

class FileViewEvents(QObject):
    file_paths_list_updated = Signal(list)
    file_selected = Signal(str)
    processing_requested = Signal()
```

### Widget Implementation
```python
# ui/view/components/file_pane/ud_file_view.py
from ui.events.file_events import FileViewEvents

class UDFileView(BasePane):
    def __init__(self):
        super().__init__()
        self.events = FileViewEvents()  # EXPLICIT EVENT OBJECT

    def add_files(self, files: List[str]):
        # Interface method - direct action
        self._add_files_internal(files)
        # Publish event for other components
        self.events.file_paths_list_updated.emit(self.get_files())
```

**CALLING CODE IS CRYSTAL CLEAR:**
- `widget.add_files(files)` = Direct method call (interface)
- `widget.events.file_paths_list_updated.connect(handler)` = Event subscription

## DECISION 3: PRESENTER EVENT HANDLING (FINAL)

**PRESENTER DOES NOT SUBSCRIBE DIRECTLY TO WIDGETS** - You're absolutely right.

### How Events Actually Flow
```python
# ud_presenter.py (TOP LEVEL)
class UpdateDataPresenter:
    def _connect_signals(self):
        # Presenter connects to VIEW, not individual widgets
        self.view.file_list_changed.connect(self._on_file_list_changed)

    def _on_file_list_changed(self, files: List[str]):
        # Update presenter state
        self.current_files = files
        # Update other components via their interfaces
        self.guide_pane.update_file_count(len(files))
```

### View Coordinates Widget Events
```python
# ui/view/ud_view.py
class UpdateDataView(QWidget):
    # View-level signals that presenter connects to
    file_list_changed = Signal(list)

    def _setup_components(self):
        self.file_view = UDFileView()
        # View subscribes to widget events
        self.file_view.events.file_paths_list_updated.connect(
            self.file_list_changed.emit  # Forward to presenter
        )
```

**CLEAN SEPARATION:**
- Widget → Widget.events → View → Presenter
- Presenter never touches widget directly for events

## DECISION 4: CONCRETE FILE STRUCTURE (IMPLEMENTABLE)

**FINAL STRUCTURE** - Based on your correction that ud_presenter stays at top level:

```
update_data/
├── ud_presenter.py              # TOP LEVEL - module API/coordinator
├── ui/                          # UI-specific code
│   ├── __init__.py
│   ├── view/
│   │   ├── ud_view.py
│   │   ├── interface/
│   │   │   └── i_view_interface.py    # Methods only
│   │   └── components/
│   │       ├── center_panel.py
│   │       ├── left_panel.py
│   │       └── file_pane/
│   │           └── ud_file_view.py
│   ├── events/                  # Separate from interface
│   │   ├── __init__.py
│   │   └── file_events.py
│   └── managers/                # If keeping current managers
│       ├── state_manager.py
│       ├── file_manager.py
│       └── processing_manager.py
├── services/                    # Business logic
│   ├── file_info_service.py
│   └── local_event_bus.py
├── pipeline/                    # Data processing
│   └── dw_director.py
└── config/                      # Module config
    ├── ud_config.py
    └── defaults.yaml
```

**IMPORT EXAMPLES:**
```python
# Main presenter stays simple
from fm.modules.update_data.ud_presenter import UpdateDataPresenter

# UI components are clearly organized
from fm.modules.update_data.ui.view.ud_view import UpdateDataView
from fm.modules.update_data.ui.events.file_events import FileViewEvents
```

## DECISION 5: OPTIMAL EVENT PATTERN (FINAL ANSWER)

**SEPARATE EVENTS ARE CLEARER** - `from ui.events` is explicit and unambiguous.

### Why This Pattern Works
```python
# Crystal clear imports
from ui.events.file_events import FileViewEvents
from ui.view.interface.i_view_interface import IUpdateDataView

# Calling code knows exactly what it's doing
widget.add_files(files)                    # Interface method
widget.events.file_paths_list_updated.connect(handler)  # Event subscription
```

### Benefits
1. **Explicit Intent**: Import tells you it's events
2. **No Confusion**: Methods vs events are clearly separated
3. **Type Safety**: Events have proper QObject parent
4. **Maintainable**: Easy to find and modify event definitions
5. **Testable**: Events can be mocked independently

**THIS IS THE OPTIMAL PATTERN** - Clear, explicit, maintainable.

---

## CONCRETE IMPLEMENTATION CODE

### File: ui/events/file_events.py
```python
from PySide6.QtCore import Signal, QObject

class FileViewEvents(QObject):
    """Events published by file view widget"""
    file_paths_list_updated = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)              # Selected file path
    processing_requested = Signal()          # User wants to process files
```

### File: ui/view/interface/i_view_interface.py
```python
from typing import Protocol, List

class IUpdateDataView(Protocol):
    """Interface for update data view - METHODS ONLY"""
    def add_files(self, files: List[str]) -> None: ...
    def remove_file(self, file_path: str) -> None: ...
    def get_current_files(self) -> List[str]: ...
    def set_processing_state(self, processing: bool) -> None: ...
    def show_error(self, message: str) -> None: ...
```

### File: ui/view/components/file_pane/ud_file_view.py
```python
from PySide6.QtWidgets import QWidget
from fm.gui.components.shared.base_pane import BasePane
from ui.events.file_events import FileViewEvents

class UDFileView(BasePane):
    """Smart file view widget with clean API"""

    def __init__(self, file_info_service):
        super().__init__()
        self.events = FileViewEvents()  # EXPLICIT event object
        self._file_info_service = file_info_service
        self._files = []  # Internal display state
        self._setup_ui()

    def add_files(self, files: List[str]):
        """Interface method - add files programmatically"""
        for file in files:
            self._add_file_internal(file)
        self.events.file_paths_list_updated.emit(self.get_files())

    def remove_file(self, file_path: str):
        """Interface method - remove file programmatically"""
        self._remove_file_internal(file_path)
        self.events.file_paths_list_updated.emit(self.get_files())

    def get_files(self) -> List[str]:
        """Interface method - get current files"""
        return [f.path for f in self._files]

    def _handle_user_add_files(self):
        """Internal - user clicked add button"""
        # Handle file dialog internally
        files = self._show_file_dialog()
        if files:
            self.add_files(files)  # Use interface method
```

---

## IMPLEMENTATION DECISIONS SUMMARY

1. **ud_presenter.py stays at TOP LEVEL** - It's the module coordinator
2. **Interface has METHODS ONLY** - No floating signals in Protocol
3. **Events are SEPARATE imports** - `from ui.events` is crystal clear
4. **Presenter connects to VIEW, not widgets** - Clean separation
5. **Widget has explicit .events object** - No confusion about what's an event

### READY TO IMPLEMENT
- ✅ Folder structure defined
- ✅ Event pattern decided
- ✅ Interface pattern decided
- ✅ Presenter connection pattern decided
- ✅ Concrete code examples provided

**NO MORE ARCHITECTURAL DEBATES** - These decisions are final and implementable.

---

## IMPLEMENTATION STEPS (CONCRETE)

### Step 1: Create Folder Structure
```bash
mkdir -p ui/view/interface
mkdir -p ui/view/components/file_pane
mkdir -p ui/events
mkdir -p ui/managers
```

### Step 2: Create Event Definitions
Create `ui/events/file_events.py` with the FileViewEvents class above.

### Step 3: Create Interface
Create `ui/view/interface/i_view_interface.py` with methods only.

### Step 4: Implement Widget
Create `ui/view/components/file_pane/ud_file_view.py` with explicit .events object.

### Step 5: Update View
Modify `ui/view/ud_view.py` to forward widget events to view-level signals.

### Step 6: Update Presenter
Modify `ud_presenter.py` to connect to view signals, not widget signals.

**THAT'S IT** - No more analysis needed. These are implementable steps.
