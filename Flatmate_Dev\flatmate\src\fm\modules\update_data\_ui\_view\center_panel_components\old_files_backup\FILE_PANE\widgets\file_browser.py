"""
File Display Widget for showing file information.
"""

import os

from fm.gui.styles.button_types import ButtonType
from fm.gui.services.info_bar_service import InfoBarService
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QFileDialog, QHBoxLayout, QHeaderView, QLabel, QMenu, QSizePolicy, QStyle,
                               QTreeWidget, QTreeWidgetItem, QVBoxLayout,
                               QWidget)

from .buttons import PanelActionButton

from ....pipeline.file_display_helper import FileDisplayHelper
from fm.core.services.logger import log


class FileDisplayWidget(QWidget):
    """Widget for displaying source files and job info."""
    
    # Signals for communicating with subscribers
    file_removed = Signal(str)  # Publishes path of removed file
    file_selected = Signal(str)  # Publishes path of selected file
    add_files_requested = Signal()  # Publishes request to add files
    files_added = Signal(list)  # Publishes list of added files
    
    def __init__(self, parent=None):
        """Initialize the file display widget."""
        super().__init__(parent)
        
        # Get the InfoBarService instance
        self.info_bar_service = InfoBarService.get_instance() # *should file browse widget acess the info bar?
        
        self._init_ui()
        self._connect_signals()
        
        # Keep track of folder items
        self.folder_items = {}
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # File tree - set to expand and fill available space
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels([
            "Name", "Size", "Status"
        ])
        self.file_tree.header().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.file_tree.header().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.file_tree.header().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.file_tree.setAlternatingRowColors(False)
        
        # Enable multiple selection with modifiers (Ctrl/Command, Shift)
        self.file_tree.setSelectionMode(QTreeWidget.SelectionMode.ExtendedSelection)
        
        # Set size policy to make the tree widget size to fit contents by default
        self.file_tree.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.file_tree.setMinimumHeight(100)  # Set a smaller minimum height
        
        # Make the tree resize to content by default
        self.file_tree.setSizeAdjustPolicy(QTreeWidget.SizeAdjustPolicy.AdjustToContents)
        
        # Add tree to layout with a stretch factor to make it expand
        layout.addWidget(self.file_tree, 1)  # The 1 is the stretch factor
        
        # Buttons - with minimal spacing to cleave to the underside of the browser
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(0, 2, 0, 0)  # Minimal top margin to cleave to browser
        layout.setSpacing(2)  # Tighter spacing between widgets
        
        # Push buttons to the right
        btn_layout.addStretch(1)
        
        # Add files button
        self.add_btn = PanelActionButton(
            text="Add files...",
            button_type=ButtonType.PRIMARY,
            tooltip="Open file selection dialog"
        )
        # Set fixed width to make buttons the same size
        self.add_btn.setMinimumWidth(120)
        btn_layout.addWidget(self.add_btn)
        
        # Remove selected button
        self.remove_btn = PanelActionButton(
            text="Remove selected",
            button_type=ButtonType.SECONDARY,
            tooltip="Remove selected files from the list"
        )
        # Set fixed width to make buttons the same size
        self.remove_btn.setMinimumWidth(120)
        self.remove_btn.setEnabled(False)
        btn_layout.addWidget(self.remove_btn)
        
        # Add button layout to main layout
        layout.addLayout(btn_layout)
        
        # We'll use the global InfoBarService instead of a local info bar
        # Get the InfoBarService instance
        self.info_bar_service = InfoBarService.get_instance()
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Connect tree selection to button enable state
        self.file_tree.itemSelectionChanged.connect(self._handle_selection_change)
        
        # Connect add button to trigger file selection dialog
        self.add_btn.clicked.connect(self._trigger_file_dialog)
        
        # Connect remove button
        self.remove_btn.clicked.connect(self._handle_remove_clicked)
        
        # Connect tree item double-click
        self.file_tree.itemDoubleClicked.connect(self._handle_item_double_clicked)
        
        # Set up context menu
        self.file_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.file_tree.customContextMenuRequested.connect(self._show_context_menu)
        
        # Set up keyboard shortcut for delete
        self.file_tree.keyPressEvent = self._handle_key_press
    
    def _handle_selection_change(self):
        """Handle selection change in the tree."""
        # Get selected items
        selected_items = self.file_tree.selectedItems()
        
        # Enable remove button if items are selected
        self.remove_btn.setEnabled(len(selected_items) > 0)
        
        # Always show the InfoBar first
        self.info_bar_service.show()
        
        # Update info bar with selection information
        if len(selected_items) == 1:
            self.info_bar_service.publish_message(f"1 file selected")
        elif len(selected_items) > 1:
            self.info_bar_service.publish_message(f"{len(selected_items)} files selected")
        else:
            # Check if there are any files in the tree
            total_files = len(self.get_files())
            if total_files == 0:
                self.info_bar_service.publish_message("No files selected, add files to process...")
            elif total_files == 1:
                self.info_bar_service.publish_message("1 file loaded, ready to process")
            else:
                self.info_bar_service.publish_message(f"{total_files} files loaded, ready to process")
    
    def _handle_remove_clicked(self):
        """Handle remove button click."""
        selected_items = self.file_tree.selectedItems()
        for item in selected_items:
            # Get file path from item data
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path:
                # Emit signal with file path
                self.file_removed.emit(file_path)
                
                # Remove item from tree
                parent = item.parent()
                if parent:
                    parent.takeChild(parent.indexOfChild(item))
                    
                    # If parent has no more children, remove it too
                    if parent.childCount() == 0 and parent != self.file_tree.topLevelItem(0):
                        parent_of_parent = parent.parent()
                        if parent_of_parent:
                            parent_of_parent.takeChild(parent_of_parent.indexOfChild(parent))
                        else:
                            self.file_tree.takeTopLevelItem(self.file_tree.indexOfTopLevelItem(parent))
                else:
                    self.file_tree.takeTopLevelItem(self.file_tree.indexOfTopLevelItem(item))
                    
        # Update info bar
        self._update_info_bar()
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        self.file_tree.clear()
        self.folder_items = {}
        
        if not source_dir:
            source_dir = os.path.dirname(files[0]) if files else ""
        
        # Create root item
        root_name = os.path.basename(source_dir) if source_dir else "Files"
        root_item = QTreeWidgetItem(self.file_tree)
        root_item.setText(0, root_name)
        root_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon))
        self.folder_items[source_dir] = root_item
        
        # Process files using FileDisplayHelper
        file_infos = FileDisplayHelper.process_files(files)
        
        for file_path, file_info in file_infos.items():
            self._add_file_item(file_path, file_info, source_dir)

        self.file_tree.expandAll()

    def set_enriched_files(self, file_info_list: list, source_dir: str = ""):
        """Set files to display using enriched file information.

        Args:
            file_info_list: List of dictionaries with enriched file information
            source_dir: Source directory for relative paths
        """
        # Clear existing items
        self.file_tree.clear()
        self.folder_items = {}

        if not file_info_list:
            return

        # Determine source directory if not provided
        if not source_dir:
            source_dir = os.path.dirname(file_info_list[0].get('path', '')) if file_info_list else ""

        # Create root item
        root_name = os.path.basename(source_dir) if source_dir else "Files"
        root_item = QTreeWidgetItem(self.file_tree)
        root_item.setText(0, root_name)
        root_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon))
        self.folder_items[source_dir] = root_item

        # Add files using enriched information
        for file_info in file_info_list:
            file_path = file_info.get('path', '')
            if file_path:
                self._add_enriched_file_item(file_path, file_info, source_dir)

        self.file_tree.expandAll()

    def _add_enriched_file_item(self, file_path: str, file_info: dict, source_dir: str):
        """Add a file item to the tree using enriched file information.

        Args:
            file_path: Path to the file
            file_info: Enriched file information dictionary
            source_dir: Source directory for relative paths
        """
        # Get parent directory
        parent_dir = os.path.dirname(file_path)

        # Create parent folder items if needed
        parent_item = self._ensure_folder_path(parent_dir, source_dir)

        # Create file item
        file_item = QTreeWidgetItem(parent_item)
        file_item.setText(0, os.path.basename(file_path))
        file_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))

        # Set enriched file info in columns
        file_item.setText(1, file_info.get('size_str', 'N/A'))

        # Use enriched format information for status column
        display_name = file_info.get('display_name', 'Unknown Format')
        bank_type = file_info.get('bank_type', 'Unknown')
        format_type = file_info.get('format_type', 'Unknown')

        # Create a more informative status display
        if bank_type != 'Unknown' and format_type != 'Unknown':
            status = f"{bank_type} {format_type}"
        elif display_name != 'Unknown Format':
            status = display_name
        else:
            status = "Unrecognized"

        file_item.setText(2, status)

        # Store file path in item data
        file_item.setData(0, Qt.ItemDataRole.UserRole, file_path)

    def _add_file_item(self, file_path: str, file_info: dict, source_dir: str):
        """Add a file item to the tree.
        
        Args:
            file_path: Path to the file
            file_info: File information dictionary
            source_dir: Source directory for relative paths
        """
        # Get parent folder
        parent_dir = os.path.dirname(file_path)
        
        # Create parent folder items if needed
        parent_item = self._ensure_folder_path(parent_dir, source_dir)
        
        # Create file item
        file_item = QTreeWidgetItem(parent_item)
        file_item.setText(0, os.path.basename(file_path))
        file_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        
        # Set file info
        file_item.setText(1, file_info.get('size_str', 'N/A'))
        
        # Set status based on pre-formatted display name from the helper
        status = file_info.get('display_name', 'Unknown Format')
        file_item.setText(2, status)
        
        # Store file path in item data
        file_item.setData(0, Qt.ItemDataRole.UserRole, file_path)
    
    def _ensure_folder_path(self, folder_path: str, source_dir: str) -> QTreeWidgetItem:
        """Ensure folder path exists in the tree.
        
        Args:
            folder_path: Path to the folder
            source_dir: Source directory for relative paths
            
        Returns:
            QTreeWidgetItem: The folder item
        """
        # If folder is already in the tree, return it
        if folder_path in self.folder_items:
            return self.folder_items[folder_path]
        
        # If folder is the source directory, return root item
        if folder_path == source_dir:
            return self.folder_items[source_dir]
        
        # Create parent folder items if needed
        parent_dir = os.path.dirname(folder_path)
        parent_item = self._ensure_folder_path(parent_dir, source_dir)
        
        # Create folder item
        folder_name = os.path.basename(folder_path)
        folder_item = QTreeWidgetItem(parent_item)
        folder_item.setText(0, folder_name)
        folder_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon))
        
        # Store folder item
        self.folder_items[folder_path] = folder_item
        
        return folder_item
    
    def _handle_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle item double-click.
        
        Args:
            item: The clicked item
            column: The clicked column
        """
        # Get file path from item data
        file_path = item.data(0, Qt.ItemDataRole.UserRole)
        if file_path:
            # Emit signal with file path
            self.file_selected.emit(file_path)
            
    def _show_context_menu(self, position):
        # Only show context menu if items are selected
        if not self.file_tree.selectedItems():
            return
            
        # Create context menu
        context_menu = QMenu(self)
        
        # Add actions
        remove_action = context_menu.addAction("Remove")
        remove_action.triggered.connect(self._handle_remove_clicked)
        
        # Add separator
        context_menu.addSeparator()
        
        # Add 'Show in Finder' action (macOS specific)
        show_in_finder_action = context_menu.addAction("Show in Finder")
        show_in_finder_action.triggered.connect(self._show_in_finder)
        
        # Show the menu at the cursor position
        context_menu.exec(self.file_tree.mapToGlobal(position))
    
    def _show_in_finder(self):
        # Show selected files in system file manager
        import subprocess
        import platform

        # Get selected items
        selected_items = self.file_tree.selectedItems()

        # Show each file in Finder
        for item in selected_items:
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path and os.path.exists(file_path):
                try:
                    system = platform.system()
                    if system == "Darwin":  # macOS
                        subprocess.run(['open', '-R', file_path])
                    elif system == "Windows":
                        subprocess.run(['explorer', '/select,', file_path])
                    elif system == "Linux":
                        subprocess.run(['xdg-open', os.path.dirname(file_path)])
                    else:
                        log.warning(f"Unsupported platform for show in finder: {system}")
                except Exception as e:
                    log.error(f"Error showing file in system manager: {e}")
                
        # Update info bar
        if len(selected_items) == 1:
            self.info_bar_service.publish_message("Showing file in Finder")
        else:
            self.info_bar_service.publish_message(f"Showing {len(selected_items)} files in Finder")
        
        # Refresh the info bar after a short delay
        from PySide6.QtCore import QTimer
        QTimer.singleShot(3000, lambda: self._update_info_bar())
    
    def _handle_key_press(self, event):
        # Handle Delete or Backspace key press
        if event.key() in (Qt.Key.Key_Delete, Qt.Key.Key_Backspace):
            # If items are selected, remove them
            if self.file_tree.selectedItems():
                self._handle_remove_clicked()
                return
                
        # Pass the event to the parent class for default handling
        QTreeWidget.keyPressEvent(self.file_tree, event)
        
    def _update_info_bar(self):
        # Update info bar based on current selection state and file count
        selected_items = self.file_tree.selectedItems()
        total_files = len(self.get_files())
        
        # Always show the info bar first before publishing messages
        self.info_bar_service.show()
        
        if len(selected_items) == 1:
            self.info_bar_service.publish_message(f"1 file selected")
        elif len(selected_items) > 1:
            self.info_bar_service.publish_message(f"{len(selected_items)} files selected")
        else:
            # Check if there are any files in the tree
            if total_files == 0:
                self.info_bar_service.publish_message("No files selected, add files to process...")
            elif total_files == 1:
                self.info_bar_service.publish_message("1 file selected to process...")
            else:
                self.info_bar_service.publish_message(f"{total_files} files selected to process...")
    
    def _trigger_file_dialog(self):
        """Open file selection dialog when Add files button is clicked."""
        # Open file dialog to select multiple files
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Files",
            "",  # Start in the last used directory
            "All Files (*.*)"
        )
        
        if files:
            # Publish the files_added signal with the selected files
            self.files_added.emit(files)
            
            # Also publish the add_files_requested signal for backward compatibility
            self.add_files_requested.emit()
            
            # Update the info bar
            if len(files) == 1:
                self.info_bar_service.publish_message(f"Added 1 file")
            else:
                self.info_bar_service.publish_message(f"Added {len(files)} files")
                
            # Reset the info bar after a short delay
            from PySide6.QtCore import QTimer
            QTimer.singleShot(3000, lambda: self._update_info_bar())
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree.
        
        Returns:
            list[str]: List of file paths
        """
        file_paths = []
        
        def collect_files(item):
            # Get file path from item data
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path:
                file_paths.append(file_path)
            
            # Process child items
            for i in range(item.childCount()):
                collect_files(item.child(i))
        
        # Process all top-level items
        for i in range(self.file_tree.topLevelItemCount()):
            collect_files(self.file_tree.topLevelItem(i))
        
        return file_paths


class FileBrowser(QWidget):
    """File browser widget for displaying and managing files."""
    
    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_add_files_requested = Signal()  # Publishes request to add files
    publish_files_added = Signal(list)  # Publishes list of added files
    
    def __init__(self, parent=None):
        """Initialize the file browser."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Set the widget to expand in both directions
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # File display widget
        self.file_display = FileDisplayWidget()
        
        # Add file display to layout with stretch factor
        layout.addWidget(self.file_display, 1)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from file display
        self.file_display.file_removed.connect(self.publish_file_removed)
        self.file_display.file_selected.connect(self.publish_file_selected)
        self.file_display.add_files_requested.connect(self.publish_add_files_requested)
        self.file_display.files_added.connect(self._handle_files_added)
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.

        Args:
            files: List of file paths or filenames
            source_dir: Source directory for relative paths
        """
        log.debug(f"[FILE_BROWSER] Received set_files call - files: {len(files) if files else 0}, source_dir: {source_dir}")
        log.debug(f"[FILE_BROWSER] Files list: {files}")

        # Ensure we have valid files to display
        if not files:
            log.debug(f"[FILE_BROWSER] No files to display, clearing file_display")
            # Clear the display if no files
            self.file_display.set_files([], "")
            return

        log.debug(f"[FILE_BROWSER] Processing files to full paths")
        # Make sure we're working with full paths
        full_paths = []
        for file in files:
            if source_dir and not os.path.isabs(file):
                full_path = os.path.join(source_dir, file)
                log.debug(f"[FILE_BROWSER] Converting relative path: {file} -> {full_path}")
                full_paths.append(full_path)
            else:
                log.debug(f"[FILE_BROWSER] Using absolute path: {file}")
                full_paths.append(file)

        log.debug(f"[FILE_BROWSER] Calling file_display.set_files() with {len(full_paths)} full paths")
        # Pass the full paths to the file display widget
        self.file_display.set_files(full_paths, source_dir)
        log.debug(f"[FILE_BROWSER] file_display.set_files() completed")

    def set_enriched_files(self, file_info_list: list, source_dir: str = ""):
        """Set files to display using enriched file information.

        Args:
            file_info_list: List of dictionaries with enriched file information
            source_dir: Source directory for relative paths
        """
        log.debug(f"[FILE_BROWSER] Received set_enriched_files call - files: {len(file_info_list) if file_info_list else 0}, source_dir: {source_dir}")

        # Ensure we have valid files to display
        if not file_info_list:
            log.debug(f"[FILE_BROWSER] No files to display, clearing file_display")
            self.file_display.set_files([], "")
            return

        # Pass the enriched file information to the file display widget
        self.file_display.set_enriched_files(file_info_list, source_dir)
        log.debug(f"[FILE_BROWSER] file_display.set_enriched_files() completed")

    def _handle_files_added(self, files: list):
        """Handle files added from the file dialog.
        
        Args:
            files: List of file paths
        """
        # Add the files to the display
        if files:
            # Determine source directory from the first file
            source_dir = os.path.dirname(files[0]) if files else ""
            
            # Add files to the display
            self.set_files(files, source_dir)
            
            # Forward the signal to subscribers
            self.publish_files_added.emit(files)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        # Get files from the file display widget
        return self.file_display.get_files() if hasattr(self.file_display, 'get_files') else []
