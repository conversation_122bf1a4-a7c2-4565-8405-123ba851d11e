# Widget Signal Refactor Tasks

**Last updated:** 2025-08-03

## Objective
Strictly migrate all user-facing signals to the view layer (e.g., `UDFileView`). Remove legacy/duplicate signal logic from panels/layout managers.

## Checklist
- [ ] Review and update plan/checklist docs for accuracy
- [ ] Audit code for legacy signal definitions in panels/layouts
- [ ] Move all user-facing signals to the view
- [ ] Remove re-emissions and duplicate connections
- [ ] Test all file selection and UI flows

---
Update this checklist as tasks are completed or new requirements are discovered.
