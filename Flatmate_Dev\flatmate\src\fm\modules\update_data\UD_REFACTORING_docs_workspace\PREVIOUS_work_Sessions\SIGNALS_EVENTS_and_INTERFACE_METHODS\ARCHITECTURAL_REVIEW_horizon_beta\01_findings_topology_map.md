# Update Data: Signals · Interface Methods · Events — Findings and Topology Map

Objective: provide a precise, code-grounded picture of where signals originate, how they are routed, and where event-bus traffic overlaps with direct presenter-manager calls. Priority is eliminating double-handling and relocating GUI signal origination to the View.

Sources reviewed
- Presenter: [ud_presenter.py](../../../../update_data/ud_presenter.py)
- View: [\_ui/ud_view.py](../../_ui/ud_view.py), [\_ui/interface/i_view_interface.py](../../_ui/interface/i_view_interface.py)
- Managers: [file_management.py](../../_ui/_presenter/file_management.py), [file_list_manager.py](../../_ui/_presenter/file_list_manager.py), [processing_manager.py](../../_ui/_presenter/processing_manager.py)
- Panels: [center_panel_layout.py](../../_ui/_view/center_panel_layout.py), [left_panel_layout.py](../../_ui/_view/left_panel_layout.py), [guide_pane.py](../../_ui/_view/center_panel_components/guide_pane.py)
- Events: [local_event_bus.py](../../services/local_event_bus.py), [ui_events.py](../../_ui/ui_events.py)
- Refactor docs: [signal_refactor.md](../signal_refactor.md), [widget_signal_refactoring_plan.md](../widget_signal_refactoring_plan.md)

High-level summary
1) View layer definition is correct on paper
   - The interface [`IUpdateDataView`](../../_ui/interface/i_view_interface.py) declares consolidated signals: cancel, source_select_requested, save_select_requested, source/save option changed, process_clicked, update_database_changed.
   - [`UpdateDataView`](../../_ui/ud_view.py) defines those signals and exposes interface methods the presenter uses.

2) Layout managers still own and re-emit GUI signals
   - [`center_panel_layout.py`](../../_ui/_view/center_panel_layout.py:68-84) connects UDFileView’s signals and re-emits via CenterPanelManager publish_* signals, keeping managers in the propagation path.
   - [`left_panel_layout.py`](../../_ui/_view/left_panel_layout.py:24-33,74-97) emits a full set of signals mirroring what the View should expose; also keeps “legacy publish_…” signals. This duplicates View responsibilities.

3) Presenter wiring shows hybrid patterns
   - [`ud_presenter.py`](../../../../update_data/ud_presenter.py:149-156) correctly connects to view interface signals for most actions.
   - But it also reaches into view internals to wire file pane signals to managers (lines 157-188), bypassing the interface abstraction and anchoring connections in presenter to subcomponents. This recreates double-handling and increases coupling.

4) Local event bus vs direct calls: overlap and duplication
   - [`file_list_manager.py`](../../_ui/_presenter/file_list_manager.py) is proper source of truth for file_paths_list and emits FILE_LIST_UPDATED, FILE_ADDED, FILE_REMOVED through local bus. Good pattern.
   - Presenter also subscribes to FILE_LIST_UPDATED and then calls [`view.set_files`](../../_ui/ud_view.py:206-214), while [`ud_view.update_files_display`](../../_ui/ud_view.py:85-93) separately listens to FILE_DISPLAY_UPDATED. Two different event shapes can update the file list, which opens the door to inconsistent or duplicate updates.
   - [`processing_manager.py`](../../_ui/_presenter/processing_manager.py) emits local bus dialog events and processing events, while presenter also bridges to global bus. Risk of duplicate user feedback across InfoBar and dialogs unless sequencing is strictly defined.

5) Guide pane emits a monitoring toggle signal (publish_toggle_folder_monitoring_requested)
   - [`guide_pane.py`](../../_ui/_view/center_panel_components/guide_pane.py:272-281).
   - Presenter hooks it to FileListManager.set_folder_monitoring via view attribute access (ud_presenter lines 189-195). This should be mediated through the view-level event or interface method, not by presenter reaching into subcomponents.

Topology map (as-is)
- Source selection
  - User → LeftPanelManager widgets → LeftPanelManager signals → View re-emit to interface signals → Presenter._connect_signals routes to FileManager.handle_source_select
  - Presenter also accesses file_pane signals directly (CenterPanelManager → UDFileView) for add/remove/monitoring → routes to FileListManager
- File list updates
  - FileManager delegates set_files/add/remove to FileListManager → emits FILE_LIST_UPDATED via local bus → Presenter subscribes → Presenter calls view.set_files
  - View separately subscribes to FILE_DISPLAY_UPDATED for display updates
- Processing
  - LeftPanelManager.action_button → LeftPanelManager.process_clicked → View.process_clicked → Presenter → ProcessingManager.handle_process → emits local bus processing events and dialog request events; Presenter also subscribes to global UpdateDataEvents for stats/completion

Key violations and double-handlers
- Layout managers are still signal hubs (CenterPanelManager, LeftPanelManager) instead of pure layout, leading to re-emission and middleman behavior.
- Presenter reaches into view internals (file_pane, guide_pane) to wire signals and call methods directly, bypassing interface.
- Dual pathways update files display (Presenter reacting to FILE_LIST_UPDATED calling set_files, and View listening for FILE_DISPLAY_UPDATED). Risk for out-of-order or duplicated UI updates.
- Legacy publish_… signals remain in LeftPanelManager.

Conclusion
The intent captured in the refactor docs is correct: Widget → View → Presenter; Panels as layout only. The code is mid-migration: View has interface signals, but layout managers and presenter still own wiring that belongs in one place (View). Local bus is appropriate for manager-to-view coordination, but View should be the singular display-updater, and Presenter should not call internal view subcomponents directly nor subscribe to view-subcomponent signals.