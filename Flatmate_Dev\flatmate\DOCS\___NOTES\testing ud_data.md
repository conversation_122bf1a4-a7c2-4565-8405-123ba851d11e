select_source_group:
folder dialogue tirggerd
error on select folder

select_save_location:
select save location appear to have triggered nothign hitting select does nothing, in nothing in guide pane just a blank screen in central panel, with a border


# 2025-07-30 @ 19:29:14
I selected the folder monitor test folder and the 1st terminal output selection was generated 
An dnothing appeared to happen in the gui,
I tried to open the folder using the right click open in finder option this generated the second terminal stack trace.
in the import test folder I can see some very undesirable behaviour:
- the files with 'test in the filename are poorly  formatted and correctly will not load 
- the actual bank statements form my bank  have a very long string appended to them it looks like a compact date and time stamp and the word failed attached 
where is this behaviour being generated ? 
-this WAS NEVER my design and the correct behaviour is defined (or should still be)  in pipeline.
I manually opened windows finder, deleted the imports and put new test files in the folder - keeping the ofx which we cant yet process...
when I went back to the app
I found the old files still listed 
when I selected and removed them
the folder name remained, even though I changed the name of the actual folder, from _flatmate_auto_import_test to _flatmate_folder_monitor_test
it remains displayed in the file_pane
the guide pane still says 
"Selected 5 files for processing"
At least it showes something this is a good start 
It should show 
"n files in foldername added for processing" n\
"Moniter this folder for new files?"
[ ] enable file monitering.

## will try manually loading the new files ...

this time by folder ...
selected folder - after some time (processing...) (no indicator)
the folder name updated - 
These are all files we know how to process, and yet size is "n/a"
and Status is Unknown Format

Action: I hit process... no feedback 
the third terminal output selection resulted from this action.
however on clser examination-  I note that the info_bar now has the contradictory message:
error: Processing complete [filepath]*
(*of one of the kiwi bank files) 
On examining the database in categorise I see that no new files appear to have been added - these files are old but some are from accounts that are not mine, if they had been added I should have seen them 
# rerun the app
still no account columns found