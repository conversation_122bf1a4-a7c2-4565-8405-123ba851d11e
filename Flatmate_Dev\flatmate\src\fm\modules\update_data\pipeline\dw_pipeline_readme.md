# Data Pipeline Architecture (`dw_director` & `dw_pipeline`)

This document provides an overview of the data processing pipeline responsible for ingesting, cleaning, and storing financial transaction data from various bank statements.

## 1. System Architecture

The system is composed of two primary components:

-   **`dw_director.py`**: The orchestrator responsible for managing the overall workflow. It iterates through input files, selects the appropriate handler, and calls the pipeline to process the data.
-   **`dw_pipeline.py`**: A collection of core utility functions that perform the heavy lifting of data processing, including merging, deduplication, validation, and database updates.

## 2. Logical Data Flow

The pipeline executes in the following sequence:

1.  **Initialization**: The `dw_director` receives a job, validates the inputs, and prepares the processing environment.

2.  **File Processing & Handler Selection**:
    -   For each input file, the director uses a **handler registry** to find a compatible statement handler.
    -   Statement handlers are specialized classes responsible for reading and formatting a specific bank statement format (e.g., `KiwibankBasicCSVHandler`, `ASBStandardCSVHandler`).
    -   If no handler is found, the file is flagged as unrecognized and skipped.

3.  **Data Aggregation (`merge_dataframes`)**:
    -   Successfully processed data from all handlers is collected into a list of pandas DataFrames.
    -   These DataFrames are standardized to a master column schema (defined in the `Columns` registry) and concatenated into a single master DataFrame.

4.  **Deduplication**:
    -   Duplicate transactions are removed from the master DataFrame.
    -   The primary key for deduplication is the `Unique Id` column.
    -   If `Unique Id` is not available, a composite key of `['Date', 'Details', 'Amount', 'Balance']` is used as a fallback to ensure transaction uniqueness.

5.  **Final Validation (`validate_final_data`)**:
    -   The merged data undergoes final validation checks, such as ensuring critical columns (`Date`, `Details`, `Amount`) are present and correctly typed.
    -   It also flags suspicious transactions where the `Amount` is non-zero but the `Balance` has not changed since the previous transaction.

6.  **File & Database Operations**:
    -   **Backup**: Original source files are backed up to a timestamped directory.
    -   **Save Master File**: The final, cleaned DataFrame is saved as a master CSV file.
    -   **Update Database**: If configured, the new transactions are loaded into the SQLite database, checking for duplicates against existing records.

## 3. Key Principles

-   **Modularity**: Each statement handler is self-contained, making it easy to add support for new bank formats without altering the core pipeline logic.
-   **Separation of Concerns**: The `dw_director` handles orchestration, while `dw_pipeline` handles data manipulation. This keeps the codebase clean and maintainable.
-   **Data Integrity**: The robust deduplication and validation steps ensure the final dataset is clean and reliable.
