Selecting a folder on a Windows machine should open the file dialog with clear instructions in the title.  
This behavior is implemented in `file_selector.py`: on Windows, the folder selection uses a file dialog workaround with an instructive title (e.g., "Select Folder (select any file in the desired folder)").  
If this is not working, check the following:
- Ensure the code path for Windows (`sys.platform == 'win32'`) is being executed.
- Verify that `dialog.selected_files` is populated after the dialog closes.
- Confirm that `self.selected_folder` is set from `os.path.dirname(dialog.selected_files[0])`.
- Make sure your UI is actually using the new `file_selector.py` component and not an outdated import.

---

### Folder Selection Dialog: Root Cause & Fix (2025-08-03)

**Root Cause:**
The presenter correctly uses the view interface (`show_folder_dialog`) for folder selection, but the actual `UpdateDataView` implementation calls the native Qt dialog directly, bypassing all Windows-specific logic in `file_selector.py`. As a result, the Windows workaround and improved instructions are never triggered in production.

**Required Fix:**
Patch `UpdateDataView.show_folder_dialog` to use:
```python
from ...shared_components.file_selector import FileSelector
files = FileSelector.get_file_paths('select_folder', title=title, parent=self, start_dir=initial_dir)
if files:
    return os.path.dirname(files[0])
return ""
```
This ensures the correct cross-platform behaviour and MVP compliance.

**Summary:**
UI is hardwired to native Qt, not the platform-aware selector. Fix the view to use `file_selector.py` for all folder selection operations.