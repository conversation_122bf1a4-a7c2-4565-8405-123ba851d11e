import pandas as pd
import re

from fm.core.services.logger import log
from ._base_statement_handler import *
from fm.core.data_services.standards.columns import Columns
from typing import Optional

class ASBStandardCSVHandler(StatementHandler):
    """ASB Bank standard CSV format mapping.
    
    This handler processes ASB's standard CSV export format, which includes:
    - Metadata rows at the top with account information
    - A header row with column names
    - Transaction data with detailed payment information
    """
    
    # Statement format definition
    statement_type = StatementType(
        bank_name="ASB",
        variant="standard",
        file_type="csv"
    )
    
    # Column mapping
    columns = ColumnAttributes(
        has_col_names=True,
        colnames_in_header=False,
        has_account_column=False,
        col_names_row=6,
        data_start_row=8,
        n_source_cols=7,
        date_format='%d/%m/%Y',
        
        source_col_names=(
            "Date", "Unique Id", "Tran Type", "Cheque Number",
            "Payee", "Memo", "Amount"
        ),
        target_col_names = [
            Columns.DATE,
            Columns.UNIQUE_ID,  # Bank-provided unique ID
            Columns.PAYMENT_TYPE,
            Columns.EMPTY_COLUMN, # always empty, cheques no longer exist
            Columns.OP_NAME,
            Columns.DETAILS, # 'Memo' is now mapped here, used for concatenation
            Columns.AMOUNT,
        ],
        concat_cols_for_details = [
            Columns.OP_NAME, 
            Columns.DETAILS
        ],  # Create 'Details' from these cols
    )
    
    # Account number pattern and location
    account = AccountNumberAttributes(
        pattern=r'Bank\s+12;\s*Branch\s+(\d+);\s*Account\s+(\d+-?\d+)',
        in_metadata=True,
        location=(1, 0)
    )
    
    # Metadata structure
    metadata = SourceMetadataAttributes(
        has_metadata_rows=True,
        metadata_start=(0, 0),
        metadata_end=(5, 0)
    )

    def _extract_account_number(self, df: pd.DataFrame, filepath: Optional[str] = None):
        """Extract the full account number from ASB statement metadata.

        This method exclusively uses the metadata within the file, as it is the
        most reliable source. It constructs the full account number from the
        bank, branch, and account parts found in the file's second row.
        Example: 'Bank 12; Branch 3053; Account 0478706-50' -> '12-3053-0478706-50'
        """
        source = AccountNumberSource.FROM_METADATA
        try:
            account_line = str(df.iloc[1, 0])
            bank_match = re.search(r'Bank\s+(\d+)', account_line)
            branch_match = re.search(r'Branch\s+(\d+)', account_line)
            account_match = re.search(r'Account\s+(\d+-?\d+)', account_line)

            if bank_match and branch_match and account_match:
                bank = bank_match.group(1)
                branch = branch_match.group(1)
                account_str = account_match.group(1)
                acc_num = f"{bank}-{branch}-{account_str}"
                return (acc_num, source)


            log.warning(f"Could not extract full ASB account number from metadata line: {account_line}")
        except (IndexError, AttributeError) as e:
            log.warning(f"Failed to extract ASB account number from metadata: {e}")

        return ("", None)

# Details concatenation logic is now handled by the base class.