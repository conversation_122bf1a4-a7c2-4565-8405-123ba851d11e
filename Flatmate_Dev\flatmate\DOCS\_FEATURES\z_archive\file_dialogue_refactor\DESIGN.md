# Design: File Dialogue System Refactor

## 1. Architectural Analysis of `file_selector.py`

The current implementation in `file_selector.py` deviates significantly from the project's established MVP architecture. It functions as a collection of static utility classes that directly instantiate and manage Qt widgets, leading to several critical violations.

### Key Violations Identified:

1.  **Direct Qt Coupling**: The file directly imports `from PySide6.QtWidgets import QFileDialog`. The `FileDialogue` and `FolderDialogue` classes then instantiate `QFileDialog` directly, embedding Qt-specific code deep within the logic.

2.  **Lack of Presenter/View Separation**: The `FileDialogue` and `FolderDialogue` classes are monolithic. Their `__init__` methods are responsible for creating the widget, setting its properties, executing it (`dialog.exec()`), and processing the results. This conflates the responsibilities of a View (displaying UI) and a Presenter (handling logic).

3.  **Absence of a View Interface**: The component is invoked via direct static method calls (e.g., `FileSelector.get_file_paths()`). There is no abstraction layer or interface. The calling code is tightly coupled to this specific implementation.

## 2. Proposed Refactoring Design

To resolve these issues, the component will be refactored to follow the **Smart Widget Recipe**. This will involve creating a true MVP structure with clear separation of concerns.

### Proposed Structure:

-   **`file_selector_view.py` (The View)**
    -   Will contain a `FileSelectorView` class that inherits from a Qt widget (e.g., `QWidget`).
    -   It will be responsible for creating and configuring the `QFileDialog`.
    -   It will expose a clean public API with methods like `show_file_dialog()` and `show_folder_dialog()`.
    -   It will emit high-level signals upon completion, such as `files_selected(list)` or `folder_selected(str)`.

-   **`file_selector_presenter.py` (The Presenter)**
    -   Will contain a `FileSelectorPresenter` class.
    -   It will have **zero Qt imports**.
    -   It will be instantiated by the view and will connect to the view's signals.
    -   It will contain the logic for what happens *after* a file or folder is selected.

-   **Integration**
    -   The `UpdateDataView` will instantiate `FileSelectorView`.
    -   The `UpdateDataPresenter` will call the public methods on the view's interface (e.g., `view.file_selector.show_file_dialog()`) to trigger the action.
    -   The `UpdateDataPresenter` will connect to the `FileSelectorView`'s signals to receive the results.

This design will fully decouple the file selection logic from the rest of the application, adhere to our architectural rules, and make the component reusable and testable.
