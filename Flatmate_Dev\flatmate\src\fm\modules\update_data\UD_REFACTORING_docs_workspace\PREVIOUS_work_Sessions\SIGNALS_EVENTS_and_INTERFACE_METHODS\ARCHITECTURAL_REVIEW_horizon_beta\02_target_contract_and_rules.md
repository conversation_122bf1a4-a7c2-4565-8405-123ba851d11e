# Target Contract and Architectural Rules

Goal: Single, unambiguous flow for UI interactions and updates with strict boundaries.

Core target flow
- Widget → View → Presenter → Manager(s)
- Managers → Local Event Bus → View (display updates only)
- Presenter does not reach into view internals (sub-widgets)
- Layout managers (CenterPanelManager, LeftPanelManager) are pure layout. No signal re-emission, no business logic.

View interface contract (authoritative)
- Interface: [`IUpdateDataView`](../../_ui/interface/i_view_interface.py)
- Signals exposed (View → Presenter):
  - cancel_clicked: Signal()
  - source_select_requested: Signal(str)
  - save_select_requested: Signal()
  - source_option_changed: Signal(str)
  - save_option_changed: Signal(str)
  - process_clicked: Signal()
  - update_database_changed: Signal(bool)
- Queries and updates (Presenter → View):
  - get_save_option() -> str
  - get_update_database() -> bool
  - set_save_select_enabled(enabled: bool)
  - set_source_option(option: str)
  - set_save_path(path: str)
  - set_process_button_text(text: str)
  - set_process_enabled(enabled: bool)
  - display_selected_source(source_data: Dict[str, Any])
  - display_enriched_file_info(file_info_list: List[Dict[str, Any]])
  - show_folder_dialog(title: str, initial_dir: str) -> str
  - show_files_dialog(title: str, initial_dir: str) -> List[str]
  - show_error(message: str) -> None
  - get_current_files() -> List[str]
  - connect_file_list_manager(local_bus) -> None
  - add_files(files: List[str]) -> None
  - remove_file(file_path: str) -> None
  - set_files(files: List[str]) -> None
  - get_selected_file() -> str
  - clear_files() -> None

Rules
1) View-only signal origination
   - All GUI/user interaction signals must be emitted by the View.
   - No signal definitions in CenterPanelManager or LeftPanelManager that mirror interface signals.
   - Panels may connect widget signals internally but only for layout-local effects (toggle visibility, layout state), not for publishing domain events.

2) Presenter-interface boundary
   - Presenter connects only to IUpdateDataView signals and methods.
   - No getattr/hasattr walks into sub-widgets (file_pane, guide_pane). If a new interaction is needed, elevate to interface or use local bus from manager to View.

3) Manager communications
   - Presenter calls managers via direct methods for local coordination (e.g., FileManager.handle_source_select, ProcessingManager.handle_process).
   - Managers emit typed dataclass events on LocalEventBus when the View must update display (e.g., FileListUpdatedEvent, FileDisplayUpdateEvent).
   - Prefer direct method calls between Presenter and Managers when within the same module and synchronous; use LocalEventBus only for View updates and async fan-out.

4) Display updates single path
   - View subscribes to a single class of display events for files (choose one):
     Option A: View listens to FILE_LIST_UPDATED and updates the file list directly.
     Option B: Managers emit FILE_DISPLAY_UPDATED only, and View updates display.
   - Presenter should not call set_files directly if View is already subscribed to the display event. Avoid double updates.

5) Guide pane integration
   - Guide pane’s publish_toggle_folder_monitoring_requested remains internal to View; View translates it to a view-level signal or emits appropriate local bus event.
   - Presenter must not subscribe to guide pane signals directly.

6) Event schema
   - Use dataclasses defined in [`ui_events.py`](../../_ui/ui_events.py) for all local bus payloads. Avoid dictionaries for new code.
   - If bridging to global bus, apply a transform function that maps dataclass to expected dict.

Mermaid diagram: target flow
```mermaid
flowchart LR
  subgraph UI
    W[Widgets] --> V[UpdateDataView]
    V -->|signals| P[Presenter]
  end
  P -->|calls| FM[FileManager]
  P -->|calls| FLM[FileListManager]
  P -->|calls| PM[ProcessingManager]

  FM -->|set_files/add/remove| FLM
  FLM -->|emit FileListUpdatedEvent| BUS[LocalEventBus]
  PM -->|emit Processing events| BUS

  BUS -->|subscribe| V
  V -->|render UI| UI_OUT[Display]
```

Compliance checkpoints
- No .emit on CenterPanelManager/LeftPanelManager that duplicates interface signals.
- Presenter does not use view.center_display.file_pane or view.guide_pane directly.
- Exactly one path updates file list in the View.
- LocalEventBus payloads are dataclasses, no dicts added by new changes.

Migration notes
- If existing functionality needs panel-level re-emission for now, mark as TEMP and route through View immediately with TODO to remove. Avoid Presenter wiring to panels.

Success criteria
- Widget → View → Presenter → Manager canonized for inputs.
- Managers → LocalEventBus → View canonized for outputs.
- Panels become strictly layout containers with zero domain events.
- No double handling of file list or processing events.