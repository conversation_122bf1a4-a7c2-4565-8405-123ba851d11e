﻿Process	Working Set	Command Line	CPU	PID	Private Bytes	Description	Company Name	Window Title	Package Name	Autostart Location	VirusTotal
node.exe	43,132 K	C:\nvm4w\nodejs\node.exe C:\ProgramData\nvm\v22.14.0\node_modules\pm2\lib\Daemon.js	< 0.01	12540	59,644 K	Node.js JavaScript Runtime	Node.js				0/77
node.exe	45,736 K	C:\nvm4w\nodejs\node.exe C:\ProgramData\nvm\v22.14.0\node_modules\pm2\lib\ProcessContainer.js	< 0.01	9492	55,404 K	Node.js JavaScript Runtime	Node.js				0/77
node.exe	68,784 K	"C:\nvm4w\nodejs\\node.exe"  "C:\nvm4w\nodejs\node_modules\npm\bin\npx-cli.js" -y tavily-mcp@0.1.3		19592	82,232 K	Node.js JavaScript Runtime	Node.js				0/77
node.exe	51,964 K	"node"   "C:\Users\<USER>\AppData\Local\npm-cache\_npx\60c84a6122ba6a4c\node_modules\.bin\\..\tavily-mcp\build\index.js" 		17252	59,420 K	Node.js JavaScript Runtime	Node.js				0/77
node.exe	46,068 K	C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs/node_modules/pm2/bin/pm2 logs sequential-thought-mcp --lines 20		10952	54,468 K	Node.js JavaScript Runtime	Node.js				0/77
node.exe	46,076 K	C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs/node_modules/pm2/bin/pm2 logs sequential-thought-mcp --lines 30		14864	54,464 K	Node.js JavaScript Runtime	Node.js				0/77

Process: node.exe Pid: 9492

Type	Name
Desktop	\Default
Directory	\KnownDlls
Directory	\Sessions\1\BaseNamedObjects
File	\Device\ConDrv\Connect
File	\Device\ConDrv\Reference
File	\Device\ConDrv\Output
File	\Device\ConDrv\Output
File	\Device\KsecDD
File	C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate
File	\Device\KsecDD
File	C:\Windows\System32\en-US\mswsock.dll.mui
File	\Device\ConDrv\CurrentOut
File	\Device\CNG
File	C:\Users\<USER>\Users\Admin\.pm2\logs\sequential-thought-mcp-error-0.log
File	C:\Users\<USER>\.pm2\logs\sequential-thought-mcp-out-0.log
File	\Device\NamedPipe\uv\18446744073709551615-12540
File	\Device\NamedPipe\uv\18446744073709551615-11160
File	C:\Users\<USER>\.pm2\pm2.log
File	C:\Users\<USER>\.pm2\pm2.log
Key	HKLM\SYSTEM\ControlSet001\Control\Session Manager
Key	HKLM\SYSTEM\ControlSet001\Control\Nls\Sorting\Versions
Key	HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options
Key	HKLM
Key	HKLM
Key	HKLM\SOFTWARE\Microsoft\Ole
Key	HKCU\Software\Classes\Local Settings\Software\Microsoft
Key	HKCU\Software\Classes\Local Settings
Key	HKLM\SYSTEM\ControlSet001\Services\WinSock2\Parameters\Protocol_Catalog9
Key	HKLM\SYSTEM\ControlSet001\Services\WinSock2\Parameters\NameSpace_Catalog5
Key	HKCU\SOFTWARE\Microsoft\Windows NT\CurrentVersion
Mutant	\Sessions\1\BaseNamedObjects\SM0:9492:304:WilStaging_02
Mutant	\BaseNamedObjects\msys-2.0S5-1888ae32e00d56aa\cygcons.input.mutex.0
Mutant	\BaseNamedObjects\msys-2.0S5-1888ae32e00d56aa\cygcons.output.mutex.0
Section	\BaseNamedObjects\msys-2.0S5-1888ae32e00d56aa\S-1-5-21-1548001105-2840173884-2425044696-1001.1
Section	\BaseNamedObjects\msys-2.0S5-1888ae32e00d56aa\1888ae32e00d56aa-cons0x9D08DE.0
Section	\Sessions\1\BaseNamedObjects\node-debug-handler-9492
Semaphore	\Sessions\1\BaseNamedObjects\SM0:9492:304:WilStaging_02_p0
Semaphore	\Sessions\1\BaseNamedObjects\SM0:9492:304:WilStaging_02_p0h
Thread	node.exe(9492): 19392
Thread	node.exe(9492): 10896
Thread	node.exe(9492): 8188
Thread	node.exe(9492): 13856
Thread	node.exe(9492): 20008
Thread	node.exe(9492): 18728
Thread	node.exe(9492): 19392
Thread	node.exe(9492): 17012
Thread	node.exe(9492): 18512
Thread	node.exe(9492): 18636
Thread	node.exe(9492): 9456
WindowStation	\Sessions\1\Windows\WindowStations\WinSta0
WindowStation	\Sessions\1\Windows\WindowStations\WinSta0
