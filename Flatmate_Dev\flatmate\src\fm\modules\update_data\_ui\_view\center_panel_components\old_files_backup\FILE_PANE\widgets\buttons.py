"""
Configurable Panel Action Button Widget.

Provides a flexible, styleable button for various panel actions.
"""

# Import application-wide styles
from fm.gui.styles.button_types import ButtonType
from PySide6.QtWidgets import QPushButton


class PanelActionButton(QPushButton):
    """
    A customizable button for panel-specific actions.
    
    Supports:
    - Dynamic text and icon
    - Consistent application-wide styling
    - State management (enabled/disabled)
    - Optional tooltip
    """
    
    def __init__(self, 
                 text="Action", 
                 icon=None, 
                 tooltip=None, 
                 button_type=ButtonType.PRIMARY, 
                 compact=False,
                 parent=None):
        """
        Initialize a panel action button.
        
        Args:
            text (str): Button text
            icon (QIcon, optional): Button icon
            tooltip (str, optional): Hover tooltip
            button_type (str): Style type ('primary' or 'secondary')
            compact (bool): Whether to use compact button style
            parent (QWidget, optional): Parent widget
        """
        super().__init__(text, parent)
        
        # Set icon if provided
        if icon:
            self.setIcon(icon)
        
        # Set tooltip
        if tooltip:
            self.setToolTip(tooltip)
        
        # Apply styling based on button type
        self._apply_style(button_type, compact)
    
    def _apply_style(self, button_type=ButtonType.PRIMARY, compact=False):
        """
        Apply predefined styles based on button type.
        
        Args:
            button_type (str): Style type
            compact (bool): Whether to use compact button style
        """
        # Get button configuration from BUTTON_CONFIGS
        from fm.gui.styles.button_types import BUTTON_CONFIGS

        # Convert string to ButtonType if needed
        if isinstance(button_type, str):
            button_type = ButtonType.PRIMARY if button_type == 'primary' else ButtonType.SECONDARY
            
        # Get config for this button type
        config = BUTTON_CONFIGS.get(button_type, BUTTON_CONFIGS[ButtonType.PRIMARY])
        
        # Build stylesheet
        style = f"""
            QPushButton {{
                background-color: {config['background']};
                color: white;
                border: none;
                padding: {6 if compact else 10}px {10 if compact else 20}px;
                font-weight: {config['font_weight']};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {config['hover']};
            }}
            QPushButton:pressed {{
                background-color: {config['pressed']};
            }}
            QPushButton:disabled {{
                background-color: #888888;
                color: #CCCCCC;
            }}
        """
        
        self.setStyleSheet(style)
    
    @classmethod
    def create_add_button(cls, compact=False, parent=None):
        """
        Create a pre-configured 'Add' button.
        
        Returns:
            PanelActionButton: Configured add button
        """
        return cls(
            text="Add", 
            button_type=ButtonType.PRIMARY, 
            tooltip="Add new items",
            compact=compact,
            parent=parent
        )
    
    @classmethod
    def create_remove_button(cls, compact=False, parent=None):
        """
        Create a pre-configured 'Remove' button.
        
        Returns:
            PanelActionButton: Configured remove button
        """
        return cls(
            text="Remove", 
            button_type=ButtonType.SECONDARY, 
            tooltip="Remove selected items",
            compact=compact,
            parent=parent
        )
    
    def set_enabled_with_style(self, enabled=True):
        """
        Set button enabled state with visual feedback.
        
        Args:
            enabled (bool): Button enabled state
        """
        self.setEnabled(enabled)
