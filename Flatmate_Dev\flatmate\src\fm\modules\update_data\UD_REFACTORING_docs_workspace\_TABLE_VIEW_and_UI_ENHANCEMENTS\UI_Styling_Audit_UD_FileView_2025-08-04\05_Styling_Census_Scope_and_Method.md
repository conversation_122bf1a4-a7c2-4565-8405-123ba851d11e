# 05 — Styling Census: Scope and Method (App-Wide)

Objective
Perform an app-wide widget styling census to document how widgets are styled today and how theme.qss relies on palette.qss (and potentially style.qss). Produce a dependency map and actionable findings without code changes.

Deliverables (to be added in this folder)
- 06_Widget_Catalog.md — Representative widgets/components and where they get styles
- 07_Stylesheet_Dependencies.md — How theme.qss, palette.qss, and style.qss interact (load order and overrides)
- 08_Findings_and_Risks.md — Systemic issues, conflicts, and governance gaps
- 09_Minimal_Policy_Proposal.md — Short policy to reduce conflicts without large refactors

Census Focus Areas
1) Stylesheet Sources
   - palette.qss: token variables used by theme.qss
   - theme.qss: variable-driven selectors
   - style.qss: global/legacy selectors and hard-coded colors
   - Any inline setStyleSheet usage in code (rare but impactful)

2) Widget Classes and Hooks
   - QTreeWidget, QTableView, QHeaderView, QScrollArea, QPushButton, QToolButton, QComboBox, QCheckBox, QFrame, QLabel, QWidget
   - objectName conventions (e.g., #file_tree, #left_panel)
   - property selectors (e.g., QPushButton[type="primary"])

3) Shared Components and Base Classes
   - fm/gui/_shared_components/* (widgets, base, toolbars)
   - Verify where shared components expect styling via properties or objectName
   - BasePane/BaseWidget patterns affecting default styles

4) Representative Modules (breadth, not depth)
   - update_data (UDFileView and peers)
   - categorize (tables/toolbars if present)
   - main window shell (left/right panels)
   - shared table view v2 components

Method
1) Inventory
   - Search for objectName assignments and setProperty calls across GUI and shared components
   - Identify setStyleSheet usage (flag for review)
   - Build a list of selectors used in theme.qss (IDs, classes, properties) and locate matching widgets

2) Map Dependencies
   - Confirm palette.qss import in theme.qss
   - Note any imports/includes in style.qss
   - Determine expected load order (doc/bootstrapping code reference)

3) Classify Risks
   - Global selectors in style.qss that collide with theme intent
   - Missing objectName/property hooks for theme selectors
   - Commented-out blocks that indicate partial migrations

4) Summarize and Recommend
   - App-wide patterns and exceptions
   - Minimal governance fixes (naming conventions, precedence rules, “no inline style” zones)
   - Short checklist for new/refactored widgets

Execution Notes
- This is read-only analysis. No code or stylesheet changes in this pass.
- Keep scope wide but shallow; capture representative examples with links.
- Prioritize clarity over completeness; we can iterate.

Next Step
Proceed to generate 06_Widget_Catalog.md and 07_Stylesheet_Dependencies.md by scanning shared components and key modules.