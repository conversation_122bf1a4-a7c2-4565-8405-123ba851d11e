# Tasks: File Dialogue System Refactor

This document breaks down the file dialogue refactoring work into atomic, sequential tasks. Each task should be completed in order.

---

### Phase 1: Create the New Component Structure

**Task 1.1: Create Directory and Files**
-   **Action**: Create a new directory: `flatmate/src/fm/gui/components/file_selector/`.
-   **Action**: Inside the new directory, create the following empty files:
    -   `__init__.py`
    -   `file_selector_view.py`
    -   `file_selector_presenter.py`
    -   `config.py`

### Phase 2: Implement the View

**Task 2.1: Implement `FileSelectorView`**
-   **File**: `file_selector_view.py`
-   **Action**: Create the `FileSelectorView(QWidget)` class.
-   **Action**: Define the high-level signals: `files_selected = pyqtSignal(list)` and `folder_selected = pyqtSignal(str)`.
-   **Action**: Implement the public API methods `show_file_dialog()` and `show_folder_dialog()`.
-   **Action**: Move the `QFileDialog` creation, configuration, and execution logic from the old `file_selector.py` into these new methods. The methods should emit the appropriate signal upon completion.

### Phase 3: Implement the Presenter

**Task 3.1: Implement `FileSelectorPresenter`**
-   **File**: `file_selector_presenter.py`
-   **Action**: Create the `FileSelectorPresenter` class.
-   **Action**: Ensure the file has **zero** `PyQt` or `PySide` imports.
-   **Action**: The presenter's `__init__` method should accept the `view` as an argument. For now, it can be a simple class with a placeholder `__init__`.

### Phase 4: Integration and Cleanup

**Task 4.1: Integrate into `UpdateDataView`**
-   **File**: `flatmate/src/fm/modules/update_data/_ui/_view/update_data_view.py`
-   **Action**: Import the new `FileSelectorView`.
-   **Action**: Instantiate `FileSelectorView` within the `UpdateDataView`'s `__init__` method.
-   **Action**: Update the methods that currently call the old `FileSelector` to instead call the public methods on the new `self.file_selector_view` instance.

**Task 4.2: Remove Old Component**
-   **Action**: Delete the file `flatmate/src/fm/modules/update_data/_ui/_view/shared_components/file_selector.py`.
-   **Action**: Search the codebase for any remaining imports of the old `FileSelector` and remove them.

**Task 4.3: Final Testing**
-   **Action**: Run the application and manually test the "Add Files" and "Add Folder" buttons to ensure the new file dialogue system works as expected.
