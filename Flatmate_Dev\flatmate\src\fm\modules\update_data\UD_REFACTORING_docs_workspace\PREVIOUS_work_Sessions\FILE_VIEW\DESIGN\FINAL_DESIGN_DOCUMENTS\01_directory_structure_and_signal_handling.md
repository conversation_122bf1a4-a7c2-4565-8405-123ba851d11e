# Directory Structure and Signal Handling

## Overview

This document defines the final directory structure and signal handling approach for the Update Data module refactoring. It provides concrete decisions that resolve the architectural questions raised during the design review process.

## Directory Structure

We will adopt a **minimal change approach with clear organization** to minimize disruption while improving architecture:

```
update_data/
├── ud_presenter.py              # TOP LEVEL - module coordinator/API
├── ui/                          # UI-specific code
│   ├── __init__.py
│   ├── view/
│   │   ├── ud_view.py           # ALL SIGNAL HANDLING
│   │   ├── interface/
│   │   │   └── i_view_interface.py    # METHODS ONLY
│   │   └── components/
│   │       ├── center_panel.py       # LAYOUT MANAGER ONLY
│   │       ├── left_panel.py         # LAYOUT MANAGER ONLY
│   │       └── file_pane_v2/         # NEW COMPONENT FOLDER
│   │           ├── __init__.py
│   │           ├── ud_file_view.py   # MAIN COMPONENT
│   │           ├── components/       # INTERNAL WIDGETS
│   │           │   ├── __init__.py
│   │           │   ├── file_table.py
│   │           │   ├── add_remove_btns.py
│   │           │   └── context_menu.py
│   │           ├── models.py         # DATA STRUCTURES
│   │           ├── config.py         # CONFIGURATION
│   │           └── utils.py          # HELPER FUNCTIONS
│   ├── events/                  # SEPARATE FROM INTERFACE
│   │   ├── __init__.py
│   │   └── file_events.py       # EVENT DEFINITIONS
│   └── managers/                # UI STATE MANAGERS
├── services/                    # BUSINESS LOGIC
└── config/                      # MODULE CONFIG
```

### Key Decisions

1. **Keep `ud_presenter.py` at Top Level**: It serves as the module's main API and coordinator
2. **Create `ui/` Folder**: Contains all UI-specific code, clearly separating it from business logic
3. **Use `file_pane_v2/` Folder**: Keeps the original component in place as a reference during development
4. **Separate Events from Interface**: Events and interface methods are defined in separate files for clarity

## Signal Handling

We will adopt a **clean separation between interface methods and events** with a clear flow of communication:

### Interface Methods vs. Events

#### Interface (Methods Only)

```python
# ui/view/interface/i_view_interface.py
class IUpdateDataView(Protocol):
    """Interface for update data view - METHODS ONLY"""
    def add_files(self, files: List[str]) -> None: ...
    def remove_file(self, file_path: str) -> None: ...
    def get_current_files(self) -> List[str]: ...
    def set_processing_state(self, processing: bool) -> None: ...
    def show_error(self, message: str) -> None: ...
    # NO SIGNALS HERE - they can't exist in Protocol
```

#### Events (Separate Definition)

```python
# ui/events/file_events.py
from PySide6.QtCore import Signal, QObject

class FileViewEvents(QObject):
    """Events published by file view widget"""
    file_paths_list_updated = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)             # Selected file path
    processing_requested = Signal()         # User wants to process files
```

### Signal Flow

The signal flow will follow a clean pattern:

1. **Widget → View → Presenter**: Widgets emit events, View forwards them to Presenter
2. **Panels as Layout Managers**: Panels no longer handle signals, they are pure layout managers
3. **View as Signal Coordinator**: The View coordinates all signals between widgets and presenter

```
Widget.events → View signals → Presenter handlers
```

### Implementation Pattern

```python
# ui/view/components/file_pane_v2/ud_file_view.py
class UDFileView(BasePane):
    def __init__(self):
        super().__init__()
        self.events = FileViewEvents()  # EXPLICIT event object
        
    def add_files(self, files: List[str]):
        """Interface method - direct action"""
        self._add_files_internal(files)
        # Publish event for other components
        self.events.file_paths_list_updated.emit(self.get_files())

# ui/view/ud_view.py
class UpdateDataView(QWidget):
    # View-level signals for presenter
    file_list_changed = Signal(list)
    
    def _connect_widget_signals(self):
        # Connect widget events to view signals
        self.center_panel.file_view.events.file_paths_list_updated.connect(
            self.file_list_changed.emit
        )

# ud_presenter.py
class UpdateDataPresenter:
    def _connect_signals(self):
        # Connect to view signals, not widget signals
        self.view.file_list_changed.connect(self._on_file_list_changed)
```

## Migration Strategy

1. **Create New Structure**: Set up the new directory structure
2. **Implement New Components**: Build the new components in parallel with existing ones
3. **Update Signal Handling**: Remove signals from panels, add to view
4. **Update Presenter**: Connect presenter to view signals
5. **Test and Validate**: Ensure all functionality works as expected
6. **Remove Old Components**: Once new components are fully functional

## Benefits

1. **Clear Separation**: UI concerns are clearly separated from business logic
2. **Explicit Intent**: Interface methods vs. events are clearly distinguished
3. **Maintainable Structure**: Related files are grouped together
4. **Minimal Disruption**: Existing code continues to work during migration
5. **Testable Components**: Clean boundaries make testing easier
