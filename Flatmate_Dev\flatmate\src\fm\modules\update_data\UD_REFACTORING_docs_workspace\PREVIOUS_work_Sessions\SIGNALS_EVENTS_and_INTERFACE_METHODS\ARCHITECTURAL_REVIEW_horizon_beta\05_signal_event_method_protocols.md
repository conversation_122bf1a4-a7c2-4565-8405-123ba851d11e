# Signal, Event, and Interface Method Protocols

Purpose
Codify authoritative rules for where Qt signals are connected, when to emit signals vs publish events, and when to use direct interface methods. Incorporates requirements: View is the single place where Qt signals are connected; file_view is user-facing source of truth; dual population routes (manual selection and folder monitoring) must keep file list and display in sync; folder monitoring is per-folder, persisted to user preferences; discovered source folders should populate source options (tracked as a future item).

Authoritative connection boundary
- Rule S1: All Qt signal connections occur only in UpdateDataView.
  - No Presenter connecting to sub-widget signals.
  - No layout manager re-emitting “domain” signals.
  - Panels may connect internal widget signals strictly for local layout behavior, not for app-level interactions.

Definitions and terminology
- “Signal” = Qt signal emitted by widgets or by the View for Presenter consumption. We emit signals.
- “Event” = Message on LocalEventBus or Global bus. We publish events.
- “Interface methods” = Methods on IUpdateDataView used by Presenter to query or set UI state.

File list and display single source of truth
- User perspective: the file_view is the single source of truth for what’s selected.
- Internal model:
  - Canonical list owner: FileListManager (authoritative list).
  - Display owner: UpdateDataView renders based on canonical events.
- Sync model:
  - All list mutations go through FileListManager (set/add/remove/clear).
  - FileListManager publishes FileListUpdatedEvent.
  - UpdateDataView subscribes and updates file_view display exactly once per change.
  - Presenter does not call set_files if View is subscribed (prevents double updates).

Population routes and flows

1) Manual selection route
- Trigger: User selects files/folder via LeftPanel widgets.
- Signal path:
  - Widgets → UpdateDataView (connects widget signals)
  - UpdateDataView emits interface signal source_select_requested(str) (or save_select_requested)
  - Presenter handles interface signals → calls FileManager.handle_source_select/save_select
- Manager actions:
  - FileManager enriches files and delegates canonical list updates to FileListManager.set_files(...)
  - FileListManager publishes FileListUpdatedEvent
- View:
  - UpdateDataView receives FileListUpdatedEvent and updates file_view display once
  - Guide pane updated from StateManager via UI state events as needed

2) Folder monitoring route
- Trigger: FolderMonitorService discovers new files for a monitored folder.
- Event path:
  - Folder monitor publishes via LocalEventBus (FilesDiscoveredEvent)
  - FileListManager subscribes and add_files(new_files) → publishes FileListUpdatedEvent
- View:
  - UpdateDataView receives FileListUpdatedEvent and updates file_view display once

Folder monitoring protocol (per-folder)
- Requirement: Monitoring is enabled per folder encountered. Persist this preference.
- Ownership:
  - FileListManager maintains an in-memory map of monitored folders (already present).
  - Persistence: UpdateDataConfig or module-specific config persists the map.
    - Keys: UpdateDataKeys.Paths.MONITORED_FOLDERS (e.g., list/dict of folder→enabled).
    - On set_folder_monitoring(folder, enabled): update memory and persist via ud_config.
    - On startup: FileListManager loads persisted monitored folders and primes FolderMonitorService.
- User interaction:
  - When View detects a “folder context” (from FileListUpdatedEvent or Select Folder), it renders a “Monitor Folder” toggle in guide pane.
  - Toggle wiring:
    - Guide pane checkbox emits a Qt signal (within View only).
    - UpdateDataView translates to publish of a typed event (FolderMonitoringToggledEvent) on LocalEventBus.
    - FileListManager subscribes and invokes set_folder_monitoring(folder, enabled) → persists and updates FolderMonitorService.

Adding discovered folders to source option group (future item)
- When a new source folder is encountered (manual or monitoring), it should be recorded and surfaced in the SourceOptionsGroup as a convenience.
- Track as a backlog item:
  - Persist encountered folders in config (UpdateDataKeys.Paths.RECENT_SOURCE_FOLDERS).
  - Update LeftPanelManager’s SourceOptionsGroup API to accept a list of recent folders.
  - Update View to call a new interface method to refresh options from persisted config on load.
- Not in current refactor scope, but noted.

Decision table: When to emit signals vs publish events vs call interface methods

- User actions originating from widgets:
  - Emit View interface Qt signals for Presenter inputs (e.g., source_select_requested, save_option_changed, process_clicked).
  - DO NOT publish user-intent events from the View. The Presenter is the coordinator for user intent and connects to these interface signals.
  - Rationale: Maintains MVP directionality (View → Presenter via signals) and avoids misusing the event bus for localized UI-controller interactions.

- State/UI display updates from Managers:
  - Publish typed LocalEventBus events (e.g., FileListUpdatedEvent, UiStateUpdateEvent).
  - View subscribes and renders. Avoid Presenter driving display with method calls when an event exists.
  - Rationale: Decouple Managers and View; ensure single-path UI updates; keep Presenter slim.

- Presenter → View direct method calls:
  - Use only for immediate, small UI concerns not covered by a standard event, or for initial setup where View needs a value instantly (e.g., set_process_button_text during refresh).
  - Do not use Presenter to push file lists if View subscribes to file list events.
  - When both an event and a method exist, prefer the event to avoid double paths.

- Cross-module intent or telemetry (optional):
  - After handling a View interface signal, the Presenter MAY publish a cross-module event on the global bus if other modules must react.
  - The View must NOT publish user-intent events directly.

- Dialogs and notifications:
  - Managers publish DialogRequestEvent (error/success/info). View renders dialogs.
  - Presenter bridges to global bus only for cross-module comms (using local_event_bus setup bridges).

Implementation notes to enforce the boundary
- UpdateDataView:
  - Centralize _connect_widget_signals(): map all widget signals to interface Qt signals for user intents; publish LocalEventBus events only for Manager-facing, non-intent notifications (e.g., folder monitoring toggled).
  - Subscribe in _subscribe_to_events(): FILE_LIST_UPDATED, UI_STATE_CHANGED, dialog events.
  - Do not expose internal widget attributes to Presenter.

- Presenter:
  - Connect only to IUpdateDataView interface signals (cancel_clicked, source_select_requested, save_select_requested, source_option_changed, save_option_changed, process_clicked, update_database_changed).
  - Do not subscribe to View-originated user-intent events on the bus (there should be none).
  - For file list updates, remove subscription to FILE_LIST_UPDATED if View already subscribes.
  - Do not connect to guide_pane or file_pane signals directly.

- Panels:
  - Remove class-level Signals that mirror interface signals and any re-emission.
  - Retain local layout responsibilities only.

Pseudocode reference

UpdateDataView wiring (single place)
```python
def _connect_widget_signals(self):
    # Left panel widgets
    self.left_panel_manager.source_options_group.button_clicked.connect(
        lambda: self.source_select_requested.emit(
            self.left_panel_manager.source_options_group.get_selected_option()
        )
    )
    self.left_panel_manager.archive_options_group.button_clicked.connect(
        self.save_select_requested.emit
    )
    self.left_panel_manager.archive_options_group.option_changed.connect(
        self.save_option_changed.emit
    )
    self.left_panel_manager.database_checkbox.state_changed.connect(
        self.update_database_changed.emit
    )
    self.left_panel_manager.action_button.clicked.connect(
        self.process_clicked.emit
    )

    # Center panel UDFileView and Guide pane
    self.center_display.file_pane_v2.file_selected.connect(
        lambda path: self.local_bus.emit(ViewEvents.FILE_ADDED.value, FileAddedEvent(file_path=path))
    )
    self.center_display.guide_pane.publish_toggle_folder_monitoring_requested.connect(
        lambda enabled: self.local_bus.emit(
            ViewEvents.FOLDER_MONITORING_TOGGLED.value,
            FolderMonitoringToggledEvent(folder_path=self._current_source_folder(), enabled=enabled)
        )
    )
```

View event subscriptions (single UI update path)
```python
def _subscribe_to_events(self):
    self.local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self._on_file_list_updated)
    self.local_bus.subscribe(ViewEvents.UI_STATE_CHANGED.value, self.update_ui_state)
    self.local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, self._show_error_dialog)
    self.local_bus.subscribe(ViewEvents.SUCCESS_DIALOG_REQUESTED.value, self._show_success_dialog)

def _on_file_list_updated(self, event: FileListUpdatedEvent):
    self.center_display.set_files(event.files, event.source_path)
```

Persistence sketch for monitored folders (in FileListManager)
```python
def set_folder_monitoring(self, folder_path: str, enabled: bool):
    folder_path = str(Path(folder_path).resolve())
    self.monitored_folders[folder_path] = MonitoredFolder(path=folder_path, monitor_new_files=enabled)
    ud_config.set_value(ud_keys.Paths.MONITORED_FOLDERS, {**self._load_persisted(), folder_path: enabled})
    self.folder_monitor_service.set_folder_monitored(folder_path, enabled)

def load_persisted(self):
    data = ud_config.get_value(ud_keys.Paths.MONITORED_FOLDERS, default={})
    for p, e in data.items():
        self.monitored_folders[str(Path(p).resolve())] = MonitoredFolder(path=str(Path(p).resolve()), monitor_new_files=e)
```

Glossary
- Signal (emit): Qt-level notification emitted by widgets and by UpdateDataView for Presenter inputs; consumed by Presenter via direct Qt connections.
- Event (publish): LocalEventBus/Global bus message for module/app coordination; carries typed dataclass payloads; NOT used by the View to represent user intent to the Presenter.
- Interface method: Presenter→View call for state query or targeted UI state update where events are not suitable.

Anti-patterns (must not occur)
- View calling Presenter or Manager methods directly.
- View publishing user-intent events (use interface Qt signals instead).
- Presenter wiring Qt signals on sub-widgets or accessing view.center_display/file_pane/guide_pane directly.
- Layout managers re-emitting “domain” signals that duplicate the View interface.

This protocol ensures:
- Qt signals are only connected in UpdateDataView.
- The file_view and canonical list stay synchronized via a single event-driven path.
- Folder monitoring is per-folder, persisted, and driven by View interactions.
- Clear rules for when to emit signals, publish events, or use interface methods, preventing double-handling and boundary leaks.