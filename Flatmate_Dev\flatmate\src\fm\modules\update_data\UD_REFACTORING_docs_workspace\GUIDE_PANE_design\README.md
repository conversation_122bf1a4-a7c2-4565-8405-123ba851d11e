# Guide Pane Design & Implementation

## Purpose
The Guide Pane is the central contextual guidance and options widget for the Update Data module. It provides state-driven feedback, user instructions, and interactive options, adapting dynamically to the user's progress and system state.

## Key Insights
- **Single Source of Guidance:** All user-facing guidance, help, and options are unified in one state-driven widget.
- **State Machine:** The pane transitions through well-defined states (welcome, selection, ready, processing, success, error, etc.), each with contextual messaging and options.
- **Rich Formatting:** Supports HTML for multi-line instructions, lists, and emphasis.
- **Options & Actions:** Allows dynamic display of buttons and checkboxes for user choices (e.g., "View Results", "Monitor this folder").
- **Event Emission:** Emits signals for option/checkbox changes, enabling clean decoupling from business logic.
- **Styling:** Consistent border, padding, and color cues for different states (info, warning, error, etc.).
- **No Redundant Welcome Pane:** Welcome/intro content is handled as a state, not a separate widget.

## Architectural Benefits
- Reduces UI complexity (fewer widgets, less code duplication)
- Ensures all user guidance is consistent and easily updated
- Cleanly decouples UI from business logic via event signals
- Easily extensible for new states or options

---

## Example State Flow
1. **Welcome**: Shows intro, steps, and info
2. **Selection**: Updates as files/folders are selected
3. **Ready**: Shows process button, monitor checkbox
4. **Processing**: Shows progress, disables options
5. **Success/Error**: Shows summary, actions (view results, process more)

---

## Test Plan

### Manual UI Test
- [ ] Welcome/initial state displays formatted intro
- [ ] State transitions update message and styling
- [ ] Buttons appear for actions in relevant states
- [ ] Checkbox can be toggled and emits event
- [ ] Error state displays correct styling and message

### Automated (PySide6/QTest) Example
```python
from PySide6.QtWidgets import QApplication
from fm.modules.update_data._view_components.center_panel_components.guide_pane import GuidePaneWidget

def test_guide_pane_states(qtbot):
    widget = GuidePaneWidget()
    qtbot.addWidget(widget)
    widget.reset_to_initial()
    assert "Welcome" in widget.message_display.toPlainText()
    widget.set_state('folder_selected', {'count': 3})
    assert "Found 3 CSV files" in widget.message_display.toPlainText()
    widget.show_options([{ 'text': 'Test', 'action': 'test_action' }])
    assert widget.options_container.isVisible()
```

---

## Future Enhancements
- Optionally support markdown rendering
- Accessibility improvements (screen reader, focus cues)
- Internationalisation/localisation support
