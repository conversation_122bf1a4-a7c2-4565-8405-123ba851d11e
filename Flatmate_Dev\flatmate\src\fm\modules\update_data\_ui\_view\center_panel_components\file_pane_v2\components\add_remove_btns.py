"""
Add/Remove buttons widget for file operations.

Internal component for the file view widget.
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSpacerItem, QSizePolicy
from PySide6.QtCore import Signal
from ..config import FileConfig
from fm.core.services.logger import log


class AddRemoveButtons(QWidget):
    """Widget containing add and remove file buttons."""
    
    # Signals
    add_files_requested = Signal()
    remove_file_requested = Signal()
    
    def __init__(self, config: FileConfig, parent=None):
        super().__init__(parent)
        self._config = config
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """Set up the button layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Add files button
        self._add_btn = QPushButton("Add Files")
        self._add_btn.setToolTip("Add files to the list")
        self._add_btn.setEnabled(self._config.allow_add)
        layout.addWidget(self._add_btn)
        
        # Remove file button
        self._remove_btn = QPushButton("Remove")
        self._remove_btn.setToolTip("Remove selected file from the list")
        self._remove_btn.setEnabled(False)  # Initially disabled
        layout.addWidget(self._remove_btn)
        
        # Add spacer to push buttons to the left
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        layout.addItem(spacer)
    
    def _connect_signals(self) -> None:
        """Connect button signals."""
        self._add_btn.clicked.connect(self._on_add_clicked)
        self._remove_btn.clicked.connect(self._on_remove_clicked)
    
    def _on_add_clicked(self) -> None:
        """Handle add button click."""
        if self._config.allow_add:
            log.debug("Add files button clicked")
            self.add_files_requested.emit()
    
    def _on_remove_clicked(self) -> None:
        """Handle remove button click."""
        if self._config.allow_remove:
            log.debug("Remove file button clicked")
            self.remove_file_requested.emit()
    
    def set_remove_enabled(self, enabled: bool) -> None:
        """Enable or disable the remove button."""
        # TODO shoule only be enabled if files present 
        if self._config.allow_remove:
            self._remove_btn.setEnabled(enabled)                    
        else:
            self._remove_btn.setEnabled(False) # ! pointless config item
    
    def set_add_enabled(self, enabled: bool) -> None:
        """Enable or disable the add button."""
        if self._config.allow_add:
            self._add_btn.setEnabled(enabled)
        else:
            self._add_btn.setEnabled(False)
    
    def update_button_states(self, has_files: bool, has_selection: bool) -> None:
        """Update button states based on current state."""
        # Add button is always enabled if allowed
        self.set_add_enabled(self._config.allow_add)
        
        # Remove button is enabled only if there's a selection and removal is allowed
        self.set_remove_enabled(has_selection and self._config.allow_remove)
