# File View Component Design Document

## Overview

The File View component will be a self-contained smart widget that encapsulates all file display logic within a single component. It will follow the successful pattern established by the `CustomTableView_v2` component while addressing the specific needs of the update_data module's file management functionality.

## Architecture

### Component Structure

The component structure has been moved to a dedicated file for better maintainability and reference:

[File View Component Structure](./file_view_component_structure.md)

### BasePane Integration Rationale

The decision to inherit from `BasePane` rather than creating a standalone widget offers several important advantages:

1. **Consistent Module Structure**:
   - All center panel components in the update_data module inherit from BasePane
   - Maintains consistency with existing architecture
   - Ensures proper integration with the module's panel system

2. **Built-in Panel Features**:
   - BasePane provides standard panel features like title bar, collapse/expand functionality
   - Handles common panel behaviors and styling
   - Reduces code duplication by leveraging existing functionality

3. **Layout Management**:
   - BasePane provides a content widget where we can add our custom layout
   - Handles panel margins and spacing consistently
   - Allows us to focus on the file view functionality rather than panel mechanics

4. **Seamless Replacement**:
   - Makes it easier to replace the existing FilePane with minimal changes to the module structure
   - Maintains the same interface expected by the panel system
   - Reduces integration complexity

### Class Hierarchy

1. **UDFileView** (in `ud_file_view.py`)
   - Main component that implements the smart widget pattern
   - Provides a clean API for the presenter to interact with
   - Manages internal state and configuration
   - Composes internal components (file table, add/remove buttons)

2. **FileTable** (in `components/file_table.py`)
   - Core file display widget based on the existing implementation
   - Handles file list display and selection
   - Emits signals for file selection
   - Emits signals for file operations

3. **AddRemoveButtons** (in `components/add_remove_btns.py`)
   - Container for Add/Remove buttons functionality
   - Emits signals for add/remove actions

4. **ContextMenu** (in `components/context_menu.py`)
   - Right-click context menu implementation
   - Provides file operations through context menu

5. **FileConfig** (in `config.py`)
   - Lightweight configuration dataclass for essential file view behavior
   - Focused on practical configuration needs like display options and file operations
## Key Design Decisions

### 1. BasePane vs. QWidget Base

After reviewing what BasePane provides, the UDFileView will inherit from BasePane:
- BasePane provides useful functionality for panel management in the update_data module
- Maintains consistency with other panes in the module
- Provides standardized layout and styling for panes
- Ensures proper integration with the center panel structure

### 2. Focused Configuration System

We'll implement a streamlined FileConfig dataclass with only essential options:
- Display options (show file size, icons, type)
- File operations permissions (allow add, remove, context menu)
- Basic sorting and grouping preferences
- No unnecessary configuration options
### 3. Internal State Management

The component will manage its own internal state as a key feature:
- File list model with metadata
- Selection state tracking
- Sort order management
- File status tracking (valid/invalid, processed/unprocessed)
### 4. Clean API

The component will expose a clean, high-level API:
- `configure()` for runtime configuration
- `set_files()` for setting the file list
- `add_file()` for adding a single file
- `remove_file()` for removing a file
- `get_files()` for getting the current file list
- `get_selected_file()` for getting the currently selected file

### 5. Event Communication and Self-Contained Functionality

The component will publish events for truly asynchronous operations:
- `FileSelectedEvent` when a file is selected (if needed by multiple components)
- `FileListChangedEvent` when the file list changes (for guide pane synchronization)

Internal methods will handle all file operations directly:
- `_handle_add_button_clicked()` - Opens file dialog directly and processes selections
- `_handle_remove_button_clicked()` - Handles file removal internally
- `_update_file_display()` - Updates the UI with current file list
- `_process_file_metadata()` - Extracts and displays file information

For file dialogs:
- The component will handle file dialogs internally using a FileDialogService
- File filtering and validation happens within the component
- No presenter involvement for basic file operations

## Implementation Details

### File Model

```python
class FileViewModel:
    """Data model for the file view component."""
    
    def __init__(self):
        self.files = []  # List of FileInfo objects
        self.selected_file = None
        
    def add_file(self, file_path):
        """Add a file to the model."""
        file_info = self._create_file_info(file_path)
        self.files.append(file_info)
        return file_info
        
    def remove_file(self, file_path):
        """Remove a file from the model."""
        self.files = [f for f in self.files if f.path != file_path]
        
    def _create_file_info(self, file_path):
        """Create a FileInfo object with metadata."""
        # Extract file metadata (size, date, etc.)
        return FileInfo(path=file_path, ...)
```

### Main Component API

```python
class UDFileView(BasePane):
    """Self-contained file display component inheriting from BasePane."""
    
    # Events are published through the event bus, not signals
    # Only publish events for operations that truly need multi-component awareness
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._model = FileViewModel()
        self._config = FileConfig()
        self._file_info_service = FileInfoService()  # Use existing service
        self._file_dialog_service = FileDialogService()
        
        # Initialize layout
        self._init_layout()
        self._setup_ui()
        
    def _init_layout(self):
        """Initialize the component layout."""
        # Main layout is provided by BasePane
        # We just need to set up our internal layout
        self._main_layout = QVBoxLayout()
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(0)
        
        # Set the layout to the content widget from BasePane
        self.content_widget.setLayout(self._main_layout)
        
    def _setup_ui(self):
        """Set up the UI components."""
        # Create the file table and buttons
        self._file_table = FileTable(self)
        self._add_remove_btns = AddRemoveButtons(self)
        
        # Create layout for components
        components_layout = QVBoxLayout()
        components_layout.addWidget(self._file_table)
        components_layout.addWidget(self._add_remove_btns)
        
        # Add to main layout
        self._main_layout.addLayout(components_layout)
        
        # Connect internal signals
        self._connect_signals()
        
    def _connect_signals(self):
        """Connect internal signals."""
        # Connect component signals to internal handlers
        self._add_remove_btns.add_button_clicked.connect(self._handle_add_button_clicked)
        self._add_remove_btns.remove_button_clicked.connect(self._handle_remove_button_clicked)
        self._file_table.file_selected.connect(self._handle_file_selected)
        
    def configure(self, **kwargs):
        """Configure component behavior."""
        # Set configuration options
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
        
        # Apply configuration
        self._apply_configuration()
        return self  # For method chaining
        
    def set_files(self, files):
        """Set the files to display."""
        self._model.clear()
        for file in files:
            self._model.add_file(file)
        self._refresh_view()
        self.file_paths_list_updated.emit(self.get_files())
        return self
        
    def add_file(self, file_path):
        """Add a single file to the view."""
        file_info = self._file_info_service.get_file_info(file_path)
        self._model.add_file(file_info)
        self._refresh_view()
        self.file_paths_list_updated.emit(self.get_files())
        return self
        
    def remove_file(self, file_path):
        """Remove a file from the view."""
        self._model.remove_file(file_path)
        self._refresh_view()
        self.file_paths_list_updated.emit(self.get_files())
        return self
        
    def get_files(self):
        """Get the current list of files."""
        return [f.path for f in self._model.files]
        
    def get_selected_file(self):
        """Get the currently selected file."""
        return self._model.selected_file
        
    def _init_file_dialog_service(self):
        """Initialize the file dialog service."""
        self._file_dialog_service = FileDialogService()
        
    def _publish_event(self, event):
        """Publish an event to the event bus."""
        # Only publish events for operations that truly need multi-component awareness
        self.event_bus.publish(event)
        
    def _handle_add_button_clicked(self):
        """Handle add button click by opening file dialog directly."""
        # Use the configured file types from FileConfig
        files = self._file_dialog_service.get_files(file_types=self._config.allowed_file_types)
        if files:
            for file in files:
                self.add_file(file)
            self._publish_event(FileListChangedEvent(files=self.get_files()))
        
    def _handle_remove_button_clicked(self):
        """Handle remove button click internally."""
        selected = self._model.selected_file
        if selected:
            self.remove_file(selected)
            self._publish_event(FileListChangedEvent(files=self.get_files()))
```

## Configuration Options

The FileConfig class will include:

```python
@dataclass
class FileConfig:
    """Streamlined configuration for file view behavior and appearance."""
    
    # Display options
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    
    # File display options
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    
    # File operations
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    
    # File types
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv"]  # Default to CSV files only
    )
```

For file information, we'll use the existing file_info_service to maintain consistency and avoid duplicating functionality. This service already handles file metadata extraction efficiently and is used throughout the application.

### File Type Management

File types will be managed at multiple levels for flexibility and maintainability:

1. **Module Constants**:
   ```python
   # In update_data/constants.py
   SUPPORTED_FILE_TYPES = {
       "CSV": ["*.csv"],
       "OFX": ["*.ofx"],
       "PDF": ["*.pdf"],
   }
   
   DEFAULT_FILE_TYPES = SUPPORTED_FILE_TYPES["CSV"]
   ```

2. **Configuration-Based**:
   - The `FileConfig` includes `allowed_file_types` which defaults to CSV
   - Can be updated at runtime when new file types are supported

3. **Extension Point**:
   - The component will include a method to update supported file types:
   ```python
   def set_allowed_file_types(self, file_types):
       """Update the allowed file types for file dialogs."""
       self._config.allowed_file_types = file_types
       return self
   ```

This approach allows the update_data module to start with CSV support and easily add OFX and PDF support in the future without modifying the component's core functionality.
## Integration with Update Data Module

### In the View

```python
# In update_data/_view/center_panel.py
def _init_components(self):
    """Initialize the panel components."""
    from .ud_file_view import UDFileView
    
    self.file_view = UDFileView()
    self.main_layout.addWidget(self.file_view)
```

### In the Presenter

```python
# In update_data/ud_presenter.py
def _connect_signals(self):
    """Connect signals between view and presenter."""
    # Get the file view component
    self.file_view = self.view.center_panel.file_view
    
    # Subscribe to events if needed
    self.event_bus.subscribe(FileListChangedEvent, self._on_file_list_changed)
    
    # Use interface methods for direct communication
    # No need to connect to signals for basic file operations
    
    # Configure the component with minimal necessary options
    self.file_view.configure(
        group_by_folder=True,
        sort_by="name",
        allowed_file_types=constants.DEFAULT_FILE_TYPES  # Use module constants
    )
    
# No file dialog handling needed in the presenter
# The file view component handles this internally
```

## Migration Strategy

1. Implement the new UDFileView component in parallel with the existing file pane
2. Create unit tests to ensure functionality matches the existing implementation
3. Integrate the new component into the center panel
4. Update the presenter to use the new component API
5. Remove the old file pane implementation once the new component is fully functional

## Benefits

1. **Simplicity**: The file view handles its own UI concerns
2. **Clean API**: High-level methods instead of complex signal routing
3. **Encapsulation**: Internal state is managed within the component
4. **Consistency**: Similar to the working table_view pattern
5. **Testability**: Clear boundaries make testing easier
6. **Maintainability**: Changes are localized to the component

## Technical Considerations

1. **Styling**: Reuse existing styling from the current file pane
2. **File Operations**: Maintain all existing file operations functionality
3. **Event Integration**: Emit focused signals for truly asynchronous operations
4. **State Synchronization**: Provide access to component state when needed

## Directory Structure for Canonical Files

### Current Structure

The current directory structure follows this pattern:

```
update_data/
├── _view/                  # View components
│   ├── center_panel.py     # Main center panel
│   └── center_panel_components/
│       ├── file_pane/      # File pane component
│       │   ├── __init__.py
│       │   ├── ud_file_view.py
│       │   ├── components/
│       │   │   ├── __init__.py
│       │   │   ├── file_table.py
│       │   │   ├── add_remove_btns.py
│       │   │   └── context_menu.py
│       │   ├── models.py
│       │   ├── config.py
│       │   └── utils.py
│       └── other_components/
├── events/                 # Event definitions
│   ├── __init__.py
│   └── file_events.py
├── constants.py            # Module constants
├── ud_presenter.py         # Module presenter
└── services/               # Business logic services
```

### Rationale for Option 2

1. **Clear UI Boundary**:
   - All UI-related code is contained within the `ui` directory
   - UI-specific constants, types, and events are co-located with UI components
   - Non-UI module constants and types remain at the module level

2. **Logical Grouping**:
   - UI components (presenter, view) are grouped with their related types and events
   - Makes it clear which constants and types are UI-specific vs. module-wide

3. **Improved Discoverability**:
   - Developers working on UI components can find all related files in one place
   - Clearer separation between UI concerns and business logic

This structure aligns well with the MVP pattern by clearly separating UI concerns from business logic while keeping related UI files together.

## Module Integration Considerations

### Module Coordinator Updates

The new directory structure will require updates to the module coordinator:

```python
# Current import (example)
from fm.modules.update_data._view.center_panel_components.file_pane import FilePane

# New import with updated structure
from fm.modules.update_data._view.center_panel_components.file_pane.ud_file_view import UDFileView
```

### Module Entry Point

Consider adding a clear module entry point for better organization:

```python
# update_data/ud_main.py
from fm.modules.update_data.ud_presenter import UDPresenter
from fm.modules.update_data._view.ud_view import UDView

class UpdateDataModule:
    """Main entry point for the update_data module."""
    
    def __init__(self):
        self.view = UDView()
        self.presenter = UDPresenter(self.view)
    
    def initialize(self):
        """Initialize the module."""
        self.presenter.initialize()
        return self.view.main_widget
```

This provides a clean API for the module coordinator to interact with.

## Final Design Review

### Strengths of the Design

1. **Self-Contained Component**: The file view handles its own state and file operations internally
2. **Clean API**: High-level methods with clear names and responsibilities
3. **Proper Event Usage**: Events only for truly asynchronous operations or multi-component notifications
4. **Separation of Concerns**: Clear boundaries between component responsibilities
5. **Reuse of Patterns**: Leverages successful patterns from table_view while staying focused

### Potential Optimizations

1. **Lazy Loading**: Consider lazy initialization of services like FileDialogService
2. **Caching**: Add caching for file metadata to improve performance with large file lists
3. **Batch Operations**: Support batch file operations for better performance
4. **Progressive Loading**: For large file lists, consider loading file metadata progressively
5. **Memory Management**: Ensure proper cleanup of resources when the component is destroyed

## Conclusion

The UDFileView component represents a significant improvement in the update_data module's architecture. By adapting the smart widget pattern to our specific needs, we create a self-contained component with a clean API that encapsulates all file display logic while inheriting from BasePane for proper integration with the module structure.

### Relationship with Guide Pane

The UDFileView will not contain the guide pane, as the guide pane references multiple GUI elements beyond just the file view. However, they will share information through a dedicated event:

- The UDFileView will publish a `FileListChangedEvent` whenever the file list changes
- The presenter can subscribe to this event to update the guide pane as needed
- This maintains proper separation of concerns while ensuring components stay synchronized

This approach balances reusing the successful patterns from table_view while keeping the implementation focused on the specific needs of the file view component without unnecessary complexity.
