"""Type definitions for Update Data module."""

from enum import Enum
# TODO ? WHERE SHOULD THIS GO?

class SourceOptions(str, Enum):
    """Source selection types."""
    SELECT_FOLDER = "Select entire folder..."
    SELECT_FILES = "Select individual files..."


class SaveOptions(str, Enum):
    """Save location types."""
    SAME_AS_SOURCE = "Same as Source Files"
    SELECT_LOCATION = "Select save location..."
