# Proposal for Simplified File Selection and Data Flow

**Author:** Cascade  
**Date:** 2025-08-03  
**Status:** DRAFT

---

## 1. Executive Summary

This document addresses the need to simplify the file selection mechanism within the Update Data module. The current implementation is fragmented and has led to confusion and bugs. We propose a refined architecture that provides a clean, single-method interface for file selection while strictly adhering to the Model-View-Presenter (MVP) pattern.

The key recommendation is to consolidate file and folder selection into a single, unified `FileSelector` utility. The Presenter will use this utility to retrieve a simple list of file paths. Crucially, the Presenter will remain responsible for enriching this file data before passing it to the View. This maintains a clean separation of concerns, ensures the View remains a passive display component, and enhances the overall testability and robustness of the system.

## 2. Analysis of Architectural Roles

To clarify the proposed data flow, it's important to define the roles of each component in the file selection process according to the MVP pattern.

*   **View (`UpdateDataView`):** The View's sole responsibility is to display data and capture user input. It should be a passive component with zero business logic. It emits signals to the Presenter (e.g., `folder_select_button_clicked`) but does not know how to get or process file data.

*   **Presenter (`FileManager`):** The Presenter is the orchestrator. It listens for signals from the View, uses services and utilities to perform actions, and pushes formatted data back to the View for display. It contains all the business logic.

*   **Service/Utility (`FileSelector`, `FileInfoService`):** These are specialised tools that perform a single task. The `FileSelector`'s job is to interact with the OS file dialogs. The `FileInfoService`'s job is to take a file path and return enriched data about it.

## 3. Proposed Data Flow

Based on these roles, the recommended data flow is as follows:

1.  **User Action:** The user clicks the "Select Folder" button in the `UpdateDataView`.
2.  **Signal Emission:** The View emits a signal, `source_select_requested`, indicating the user's intent.
3.  **Presenter Handles Request:** The `FileManager` (Presenter) catches this signal. It calls the new, simplified `FileSelector` utility.
4.  **File Path Retrieval:** The `FileSelector.get_paths()` method shows the appropriate OS dialog and returns a simple `List[str]` of file paths to the Presenter.
5.  **Data Enrichment:** The Presenter takes this list of paths and uses the `FileInfoService` to enrich it, converting the list of strings into a list of `FileInfo` data objects.
6.  **View Update:** The Presenter calls a method on the view interface, e.g., `view.display_files(file_info_list)`, passing the enriched data.
7.  **Display:** The View receives the list of `FileInfo` objects and displays them in the file table. It does not know or care where they came from.

## 4. Architectural Decision: Presenter-Led Data Enrichment

As outlined above, we strongly recommend that the **Presenter remains responsible for data enrichment**. While it may seem simpler to have the View fetch file details itself, this approach has several critical disadvantages:

*   **Violates MVP:** It mixes business logic (fetching file data) with presentation logic, making the architecture inconsistent.
*   **Reduces Testability:** It becomes impossible to test the View's display logic without a real file system. With a passive View, we can pass it mock `FileInfo` objects and verify its behaviour in complete isolation.
*   **Increases Coupling:** The View becomes tightly coupled to the `FileInfoService` and the file system, making future changes more difficult.

By having the Presenter pass a well-defined data structure (`List[FileInfo]`) to the View, we create a robust, testable, and maintainable system that adheres to our established architectural patterns.

## 5. Next Steps

1.  Refactor the `FileSelector` to provide a single, static `get_paths(selection_type: str)` method.
2.  Update the `FileManager` to use this new method for all file/folder selection.
3.  Ensure the `FileManager` correctly uses the `FileInfoService` for data enrichment.
4.  Verify that the `UpdateDataView` only accepts enriched data and contains no file-accessing logic.
