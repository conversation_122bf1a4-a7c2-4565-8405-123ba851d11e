# Phased Refactor Plan — Signals · Interface · Events

Objective
Deliver a minimal-blast-radius sequence that eliminates double handling, moves all GUI signal origination to the View, restores strict Presenter↔View interface boundaries, and normalizes event-driven display updates.

Principles Recap
- Inputs path: Widget → View → EventBus → Presenter → Managers
- Outputs path: Managers → Local Event Bus → View
- Panels (Center/Left) are layout-only, no domain event propagation
- Presenter has zero knowledge of Qt signal implementations; it subscribes only to typed user-intent events on the Local Event Bus
- All Qt signal connections are contained within UpdateDataView; View translates widget signals into typed user-intent events
- Exactly one canonical path updates files display in the View

Phase 0: Branching, guardrails, and validation
- Actions
  - Create branch: refactor/update-data-signal-contract
  - Enable DEBUG logs already present in modules for verification
  - Confirm imports: presenter, view, managers load in an isolated script
- Exit criteria
  - Imports pass, app loads Update Data module without regression

Phase 1: Presenter wiring cleanup (no behavior change intended)
- Actions
  1) Move sub-widget signal wiring from presenter into view
     - From [`ud_presenter.py`](../../../../update_data/ud_presenter.py:157-195): remove all getattr/hasattr wiring that connects file_pane and guide_pane signals to managers or presenter methods.
     - In [`_ui/ud_view.py`](../../_ui/ud_view.py), add a private method _connect_widget_signals() that:
       - Subscribes to UDFileView and GuidePaneWidget Qt signals
       - Translates user actions into typed LocalEventBus user-intent events (e.g., SourceSelectRequestedEvent, SaveSelectRequestedEvent, ProcessClickedEvent, UpdateDatabaseChangedEvent)
       - Continues to publish Manager-facing notifications (e.g., FolderMonitoringToggledEvent) on the Local Event Bus
  2) Presenter subscribes only to typed user-intent events on the Local Event Bus (no Qt)
     - Replace connections to IUpdateDataView interface signals with event subscriptions (one handler per intent)
     - Remove FILE_LIST_UPDATED subscription and _on_file_list_updated if adopting Phase 2 Option A (below)
  3) Purge anti-patterns:
     - Remove any Presenter connections to sub-widget Qt signals
     - Ensure Presenter has zero Qt knowledge; it does not connect to any Qt signals, only bus events
- Exit criteria
  - Presenter no longer references view.center_display.file_pane or view.guide_pane or any Qt signal
  - All Presenter inputs are typed Local Event Bus user-intent events
  - View is the only component that knows about and connects Qt signals

Phase 2: Canonicalize file list display updates
- Decision: Keep a single event-driven path from Managers → Local Event Bus → View
- Actions
  - In [`_ui/ud_view.py`](../../_ui/ud_view.py), subscribe to ViewEvents.FILE_LIST_UPDATED in _subscribe_to_events()
  - Normalize update_files_display handler to accept FileListUpdatedEvent dataclass as the only path to update file_view display
  - Ensure Presenter does not call set_files and does not subscribe to FILE_LIST_UPDATED
- Exit criteria
  - Exactly one code path updates files display in View
  - Verified by DEBUG logs showing a single update per change

Phase 3: Layout managers become layout-only
- Actions
  - CenterPanelManager
    - Remove publish_file_selected, publish_file_removed, processing_requested, and file_list_changed emissions (replaced by View wiring to UDFileView)
    - Keep only layout: splitter, adding guide_pane and UDFileView, getters like get_files(), and display helpers
  - LeftPanelManager
    - Remove domain-level signal set mirroring the View interface; keep only widget-local signals
    - Remove legacy publish_* signals (publish_file_selected, publish_data_selected, publish_exit_selected)
  - In View, rewire: map widget-level signals from LeftPanelManager to interface signals and/or local bus events
- Exit criteria
  - No domain/event re-emission in panels
  - View is the sole place where user actions are translated to interface signals

Phase 4: Guide pane folder monitoring integration
- Actions
  - In View: subscribe to guide_pane.publish_toggle_folder_monitoring_requested and publish FolderMonitoringToggledEvent on the Local Event Bus
  - FileListManager handles FolderMonitoringToggledEvent → persists via ud_config (UpdateDataKeys.Paths.MONITORED_FOLDERS) and updates FolderMonitorService
  - Do NOT route this via Presenter unless cross-module side-effects are required
- Exit criteria
  - Presenter is not aware of guide_pane or its Qt signals
  - Toggling checkbox updates monitoring via FileListManager through the canonical path
  - Monitoring state persists across sessions

Phase 5: Dialog events normalization and processing feedback
- Actions
  - Ensure ProcessingManager emits only typed dataclass events for dialogs and lifecycle on local bus
  - View subscribes to dialog events and renders user feedback (error/success) consistently
  - Presenter remains focused on global UpdateDataEvents subscription only when cross-module communication is needed
- Exit criteria
  - No double dialogs or mixed Presenter/View dialog responsibilities
  - Log shows single user-facing feedback per event

Phase 6: Dead code and legacy removal
- Actions
  - Remove legacy publish_* signals in LeftPanelManager
  - Remove Presenter’s _on_file_list_updated when Option A is used
  - Remove any dict-based new emissions in favor of dataclasses in ui_events.py
  - Delete comment blocks referencing archived view_manager in Presenter or mark TODO to fully rehome
- Exit criteria
  - No dead or deprecated signal names remain in code
  - All local bus payloads for new code are dataclasses

Validation Plan
- Logging verification
  - Before/after comparison that for each user interaction exactly one signal path fires
  - When adding/removing files, confirm only one files display update event leads to a single View update
- Smoke test checklist
  - Select files → files list appears once, guide pane updates state
  - Select folder → file list appears once, monitor option shown; toggling monitor emits a single event and affects FileListManager
  - Change save option to SAME_AS_SOURCE → save location updates without extra UI noise
  - Click Process → single processing start event, InfoBar status updates, single completion dialog
- Optional test scaffolding
  - A minimal test that instantiates UpdateDataView and UpdateDataPresenter, triggers a FileListUpdatedEvent, and asserts a single View update

Deliverables
- Code changes aligned to phases above
- Updated architecture docs:
  - Findings and topology: 01_findings_topology_map.md
  - Target contract and rules: 02_target_contract_and_rules.md
  - Gap analysis and fixes: 03_gap_analysis_and_fixes.md
  - This plan: 04_phased_refactor_plan.md
- Post-implementation: update the developer guide to reflect the final signal contract and event model

Proposed PR sequencing
1) PR-1: Presenter wiring cleanup + View widget-signal consolidation (Phase 1), move to user-intent events via Local Event Bus and remove all Presenter Qt connections
2) PR-2: Canonical file display path (Phase 2) ensuring Manager→Bus→View single path; delete any Presenter FILE_LIST_UPDATED subscription or set_files calls
3) PR-3: Layout managers cleanup (Phase 3) + guide pane toggle routing via View→Bus→FileListManager with persistence (Phase 4)
4) PR-4: Dialog normalization + dead code removal (Phases 5 and 6)

Success definition
- No double handling for file list or processing flows
- Panels have zero domain signal ownership
- Presenter boundary is interface-only
- View is the single signal origin for user interactions and the single subscriber for display updates