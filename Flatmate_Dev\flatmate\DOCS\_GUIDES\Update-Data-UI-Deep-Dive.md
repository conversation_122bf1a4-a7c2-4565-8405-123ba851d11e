# Update Data UI Deep-Dive (Onboarding)

Last Updated: 2025-08-03
Scope: Canonical topology, channels, contracts, and patterns for Update Data UI
Audience: Engineers and AI agents implementing or modifying the Update Data module

1. Purpose and Non-Goals
- Purpose: Provide a single authoritative reference for how Update Data UI is wired: signals → intents → managers → typed events → rendering.
- Non-Goals: This is not a PRD or pipeline processing spec. It focuses on UI architecture, channel naming, ownership, and lifecycle.

2. Topology Overview (Central Switchboard Pattern)
- Core principle: The View is the switchboard. Widgets emit Qt signals; the View connects to them and translates into canonical Local Event Bus intents. Presenter/Managers subscribe to intents, perform work, and emit typed state/dialog events that the View renders or displays.
- High-level flow:
  - Widget(Qt) → View(Qt connections) → Local Bus Intents → Presenter/Managers → Typed Events → View(Render/Dialogs)

3. Canonical Channels and Ownership
3.1 Intents (ViewEvents.*, string keys)
- SOURCE_SELECT_REQUESTED: SELECT_FILES | SELECT_FOLDER
  - Emitter: View (wired from Left Panel signals)
  - Consumers: Presenter → FileManager
- add_files_requested
  - Emitter: View (wired from UDFileView.add_files_requested)
  - Consumers: FileManager (subscribe and open files dialog)
- DESTINATION_SELECT_REQUESTED (if applicable)
  - Emitter: View
  - Consumers: Presenter → FileManager
- PROCESS_REQUESTED, CANCEL_REQUESTED, UPDATE_DATABASE_CHANGED, OPTION changes, etc.
  - Emitter: View
  - Consumers: Presenter/Managers

3.2 Typed State Events (dataclasses in _ui/ui_events.py)
- FileListUpdatedEvent
  - Emitter: FileListManager
  - Consumer: View (render files pane)
- ProcessingStartedEvent, ProcessingCompletedEvent
  - Emitter: ProcessingManager
  - Consumer: View (enablement/text/status)
- DialogRequestEvent (Error/Success variants)
  - Emitter: Managers
  - Consumer: View (QMessageBox display)

4. Left Panel vs File Pane: Contract and Separation
- Left Panel
  - Nature: Module-owned composite panel (actions/options)
  - Policy: View connects to its Qt signals, translates to module intents (e.g., SOURCE_SELECT_REQUESTED with SELECT_FILES/SELECT_FOLDER).
  - Result: Managers decide dialogs and work; consistent naming and policy in one place.

- File Pane (UDFileView)
  - Nature: File list sub-widget
  - Policy: View connects to UDFileView.add_files_requested and translates to a distinct add_files_requested channel.
  - Result: FileManager subscribes and opens the files dialog via the unified FileSelector. Prevents collision with SOURCE_SELECT_REQUESTED.

Why separated? Previously both “add files” and Left Panel “Select files” shared SOURCE_SELECT_REQUESTED, causing double dialogs. Distinct channels avoid collisions while preserving canonical naming.

5. Presenter/Managers: Permissions and Responsibilities
- Presenter
  - Subscribes to intent channels, coordinates managers, sets up bridges as needed.
  - Interface calls policy:
    - Allowed: Presenter can call view interface methods when acting on high-level intents (e.g., initial morphing, non-widget-specific UI setup) where a direct, immediate UI effect is warranted.
    - Preferred for processing UI and user feedback: Event-driven. Runtime processing enablement, text, and result notifications should flow via typed events (ProcessingStarted/Completed, DialogRequest) for consistency and testability.
    - Avoid: Presenter reaching into specific sub-widgets or duplicating state-render logic that the View already performs from typed events.
  - Rationale for favoring events for processing flows:
    1) Single source of truth for UI state: View renders from typed events, reducing divergent code paths.
    2) Composability and fan-out: Multiple observers (status bar, file pane, toolbar) can react to the same lifecycle events without additional wiring.
    3) Testability and replay: Processing state can be simulated by emitting events; no need to mock deep interface calls.
    4) Collision avoidance: Central switchboard controls translation and ensures consistent timing/order.
- FileManager
  - May open file/folder dialogs via the unified FileSelector in response to intents (SOURCE_SELECT_REQUESTED or add_files_requested).
  - Delegates canonical list to FileListManager; enriches file info; updates state; emits SOURCE_DISCOVERED (if used) and relies on FileListUpdatedEvent for rendering.
- FileListManager
  - Owns canonical list; emits FileListUpdatedEvent; handles add/remove/monitor.
- ProcessingManager
  - Emits ProcessingStarted/Completed; emits DialogRequest events for user feedback.

6. Central Switchboard Responsibilities (View)
- Connect to sub-widget Qt signals (Left Panel, File Pane, buttons, toggles).
- Translate to canonical Local Event Bus intents (no direct interface calls from sub-widgets).
- Subscribe to typed state events and render UI.
- Subscribe to dialog-request events and show dialogs (error/success) only from these events.
- Policy enforcement: naming, payload normalization, collision avoidance, logging, debouncing if necessary.

7. Naming Conventions and Payloads
- Intents: String keys in ViewEvents, values are strong enums/strings (e.g., "SELECT_FILES").
- Typed events: Dataclasses for state, never bare dicts.
- Interface vs Event guidance:
  - Dialogs that solicit files/folders: Initiated by managers (e.g., FileManager) via FileSelector in response to intents.
  - Processing lifecycle and user feedback: Prefer events (ProcessingStarted/Completed, DialogRequest) over direct Presenter interface calls to keep a single rendering path.
  - Initial morphing/setup or non-runtime UI changes: Presenter may call interface methods directly when that produces clearer, simpler code without duplicating event-render logic.
- Channels:
  - SOURCE_SELECT_REQUESTED (with value SELECT_FILES | SELECT_FOLDER)
  - add_files_requested (File Pane only)
  - FILE_LIST_UPDATED, PROCESSING_STARTED, PROCESSING_COMPLETED
  - ERROR_DIALOG_REQUESTED, SUCCESS_DIALOG_REQUESTED
  - FILE_REMOVED and other module-specific

8. Unified File Selection API (Summary)
- FileSelector.get_paths(selection_type='files'|'folder', initial_dir, title, parent=view)
- FileManager._select_files/_select_folder call FileSelector and funnel results through _process_selected_files
- FileListManager.set_files becomes the canonical pipeline for list updates
- Last directory persisted via ud_config/ud_keys

9. Decision Heuristics: Widget Events vs View Translation
- Default: View translation (central switchboard) for composite/module-owned panels.
- Exception: Portable micro-widgets (e.g., relocatable toolbar) may expose a small widget-local event namespace with typed payloads, still bridged by the View to module intents. Keep keys and payload definitions centralized to avoid drift.

10. Typical Sequences (Textual)
10.1 Left Panel “Select files”
1) Left Panel emits Qt signal select_files
2) View receives signal → emits SOURCE_SELECT_REQUESTED with SELECT_FILES
3) Presenter/Managers route to FileManager._select_files → FileSelector dialog
4) FileManager processes selections → FileListManager.set_files → emits FileListUpdatedEvent
5) View updates file list rendering from event

10.2 File Pane “Add files”
1) UDFileView emits Qt signal add_files_requested
2) View receives signal → emits add_files_requested intent
3) FileManager subscribes → _select_files → FileSelector dialog
4) FileManager processes selections → FileListManager.set_files → FileListUpdatedEvent
5) View updates file list rendering from event

10.3 Processing flow
1) View emits PROCESS_REQUESTED
2) Presenter delegates to ProcessingManager (validate/run)
3) ProcessingManager emits ProcessingStartedEvent → View disables inputs/updates status
4) ProcessingManager emits DialogRequestEvent on error or success → View shows dialog
5) ProcessingManager emits ProcessingCompletedEvent → View re-enables inputs/updates status

11. Verification Checklist (Operational)
- One dialog per action:
  - Left Panel Select files/folder → exactly one dialog
  - File Pane Add files → exactly one dialog
- Single canonical rendering path:
  - FileListUpdatedEvent drives file list UI; no parallel direct updates except immediate UX affordances
- Processing lifecycle:
  - View responds to PROCESSING_STARTED/COMPLETED for enablement and status
  - Dialogs shown only for dialog-request events
- Logging:
  - Log subscription/handler entries for add_files_requested and SOURCE_SELECT_REQUESTED handling paths

12. Anti-Patterns to Avoid
- Sub-widgets calling interface methods directly for dialogs
- Multiple channels for the same intent without clear ownership
- Emitting untyped dict payloads for stateful events
- Presenter showing user feedback dialogs directly (bypass event-driven pattern)

13. Extending the UI Safely
- Add new intent: Define key in ViewEvents, wire View translation from sub-widget Qt signal.
- Add new state event: Create dataclass in _ui/ui_events.py; manager emits; View renders.
- Add widget: Keep it Qt-signal-only; let the View bridge to module intents.

14. References (Clickable)
- View wiring: [ud_view.py](flatmate/src/fm/modules/update_data/_ui/ud_view.py)
- FileManager add-files subscription: [file_management.py.subscribe_add_files_channel()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:96)
- FileManager handler: [file_management.py._on_files_add_requested()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:110)
- Canonical list manager: [file_list_manager.py](flatmate/src/fm/modules/update_data/_ui/_presenter/file_list_manager.py)
- Local bus + events enum: [local_event_bus.py](flatmate/src/fm/modules/update_data/services/local_event_bus.py)
- Typed UI events: [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py)

15. Status
- Left Panel vs File Pane split complete (SOURCE_SELECT_REQUESTED vs add_files_requested)
- Processing lifecycle and dialog-request events integrated
- Central switchboard enforced in View for signal translation and policy

Appendix A: Glossary
- Intent: An action request emitted on the Local Event Bus (string key with enum/string payload).
- Typed State Event: A dataclass-carrying state notification (e.g., FileListUpdatedEvent).
- Switchboard: The View’s role to wire Qt signals and translate to canonical intents.

Appendix B: Future Enhancements
- Add sequence diagrams (Mermaid) illustrating Left Panel and File Pane flows
- Add debouncing/throttling patterns for rapid user interactions
- Add registry/checklist for channel additions to prevent collisions and drift