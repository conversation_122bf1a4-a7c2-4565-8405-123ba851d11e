#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.

This is the main presenter class that has been decomposed into specialized managers:
- StateManager: Handles all presenter state
- WidgetStateManager: Handles UI synchronization
- SourceManager: Handles source selection and folder monitoring
- ArchiveManager: Handles save location and archive options
- ProcessingManager: Handles file processing and events

The presenter now acts primarily as a coordinator between these managers.
"""

from typing import Optional
import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import log
from ...core.services.folder_monitor_service import folder_monitor_service
from .services.file_info_service import FileInfoService

# InfoBarService imported locally to avoid circular imports
# configure_auto_import imported locally to avoid circular imports
from ..base.base_presenter import BasePresenter
from ._ui.interface import IUpdateDataView
from .config.ud_config import ud_config
from .config.ud_keys import UpdateDataKeys
from ._ui.option_types import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents

# MIGRATION: Import local event bus for event-driven architecture
from .services.local_event_bus import update_data_local_bus, ViewEvents, setup_global_event_bridges
# Import event dataclasses for type-safe event handling
from ._ui.ui_events import *

# Import state management from new location
from ._ui._presenter.state_coordinator import UpdateDataState, StateManager
from ._ui._presenter.file_management import FileManager
from ._ui._presenter.file_list_manager import FileListManager
from ._ui._presenter.processing_manager import ProcessingManager


class UpdateDataPresenter(BasePresenter):
    """
    Decomposed Update Data presenter.
    
    This presenter has been refactored from a monolithic class into a coordinated
    system of specialized managers. The presenter now primarily handles:
    - Manager instantiation and dependency injection
    - Signal routing to appropriate managers
    - Inter-manager coordination (temporary - should be refactored)
    - Module lifecycle management
    
    The actual business logic has been moved to specialized managers:
    - StateManager: All presenter state
    - WidgetStateManager: UI synchronization
    - SourceManager: Source selection and monitoring
    - ArchiveManager: Save location and options
    - ProcessingManager: File processing and events
    """

    def __init__(self, main_window, gui_config, gui_keys):
        """Initialize the decomposed presenter with manager coordination."""
        super().__init__(main_window, gui_config, gui_keys)

        # Set debug log level for Update Data module
        from ...core.services.logger import log
        
        # Directly set debug level
        log.set_level("DEBUG")
        log.debug("log level set to debug in ud_presenter.py")
        log.debug("UPDATE_DATA: Debug logging enabled for console output")
        
        # Initialize state manager first
        self.state = UpdateDataState()

        # Initialize services
        from ...gui.services.info_bar_service import InfoBarService
        self.info_bar_service = InfoBarService.get_instance()

        # MIGRATION: Add local event bus for event-driven architecture
        self.local_bus = update_data_local_bus

        # State tracking (legacy - will be migrated to ui_state)
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self._updating_source_option = False  # Flag to prevent signal loops

        # View manager for morphic UI
        # self.view_manager = UpdateDataViewManager()  # Missing - in archive

        # Initialize folder monitor service integration
        self.folder_monitor_service = folder_monitor_service
        # Callback will be registered in _connect_signals after source_manager is created

        # SimpleStateCoordinator will be initialized in _connect_signals after view creation
        self.state_coordinator = None

    def _create_view(self) -> IUpdateDataView:
        """Create the view instance. Called once during setup."""
        # Import the concrete implementation locally to avoid circular dependencies
        from ._ui.ud_view import UpdateDataView

        # Return the concrete view, but the presenter will only interact with it
        # through the IUpdateDataView interface.
        return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)

    def _connect_signals(self):
        """Connect view interface signals to handlers. Called once during setup."""
        # Initialize consolidated StateManager now that view is available
        self.state_manager = StateManager(
            self.view,
            self.info_bar_service,
            self.folder_monitor_service
        )
        # Update state reference to use consolidated manager
        self.state = self.state_manager.state

        # Initialize FileListManager before FileManager
        self.file_list_manager = FileListManager(
            folder_monitor_service=self.folder_monitor_service,
            local_bus=self.local_bus,
            file_info_service=FileInfoService()
        )

        # Initialize FileManager with FileListManager dependency
        self.file_manager = FileManager(
            self.view,
            self.state_manager,
            self.file_list_manager,  # Pass FileListManager instead of folder_monitor_service
            self.local_bus
        )

        # Register folder monitor callback with FileListManager
        self.folder_monitor_service.register_callback(self.file_list_manager._on_files_discovered)

        # Initialize ProcessingManager now that view is available
        self.processing_manager = ProcessingManager(
            self.view,
            self.state,
            self.info_bar_service,
            self.local_bus
        )

        # Presenter should not connect to Qt signals. Subscribe to typed user-intent events on local bus.
        self.local_bus.subscribe(ViewEvents.CANCEL_REQUESTED.value, lambda _e: self.request_transition("home"))
        self.local_bus.subscribe(ViewEvents.SOURCE_SELECT_REQUESTED.value, self.file_manager.handle_source_select)
        self.local_bus.subscribe(ViewEvents.DESTINATION_SELECT_REQUESTED.value, lambda _e: self.file_manager.handle_save_select())
        self.local_bus.subscribe(ViewEvents.SOURCE_OPTION_CHANGED.value, self.file_manager.handle_source_option_change)
        self.local_bus.subscribe(ViewEvents.SAVE_OPTION_CHANGED.value, self.file_manager.handle_save_option_change)
        self.local_bus.subscribe(ViewEvents.PROCESS_REQUESTED.value, lambda _e: self.processing_manager.handle_process())
        # Ensure presenter never shows dialogs directly; View handles via dialog events
        # Remove any legacy direct calls (none present here by inspection)

        # Database mode change routed via bus (Presenter updates config and UI text)
        self.local_bus.subscribe(ViewEvents.UPDATE_DATABASE_CHANGED.value, self._handle_update_database_change)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        self.state_coordinator = None
        setup_global_event_bridges()

        log.debug("Local bus subscriptions set up; Presenter has zero Qt signal knowledge")

        # Subscribe to Update Data global events using processing manager
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self.processing_manager.on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self.processing_manager.on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        # Presenter should not drive view file display; View subscribes to FILE_LIST_UPDATED itself.

    def _refresh_content(self, **params):
        """Refresh update data content when shown.

        This method is called every time the module becomes visible.
        It handles view state setup and configuration.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing UpdateData content")

        # Set the source option from config to remember user's last choice
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)

        # Configure view based on database mode using interface methods
        is_database_mode = self.view.get_update_database()
        # TODO: is_data_base... not accessed?
        # Set initial process button state
        self.view.set_process_button_text(self.state.process_button_text)

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()

        # Show the InfoBar with appropriate message
        self.info_bar_service.show()
        self.info_bar_service.publish_message(
            "Select source files or folder to begin.", "INFO"
        )

        self._setup_view_from_config()

        # Sync initial state to view (including guide pane)
        self.state_manager.sync_state_to_view()

        log.debug("UpdateData content refresh complete")

    def _handle_guide_pane_message(self, message: str):
        """Handle guide pane messages including monitor folder checkbox changes."""
        if message.startswith("checkbox_changed:monitor_folder:"):
            # Extract the checkbox state (0 = unchecked, 2 = checked)
            state = message.split(":")[-1]
            is_checked = state == "2"

            # Update the monitor folder setting
            self.source_manager.handle_monitor_folder_change(is_checked)

            log.debug(f"Monitor folder checkbox changed: {is_checked}")

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change - drives UI morphing."""
        # Store the state in configuration
        ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

        # Presenter may make interface calls, but must not open dialogs directly.
        # Keep UI morphing via interface methods only.
        if checked:
            # Align with interface: let the View own button text
            self.view.set_process_button_text("Update Database")
            status_msg = "Database mode: Files will be imported to database"
        else:
            self.view.set_process_button_text("Process Files")
            status_msg = "File utility mode: Files will be processed without database updates"

        # Also update save select enabled state if archive option implies so
        try:
            is_same_as_source = self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value
            self.view.set_save_select_enabled(not is_same_as_source)
        except Exception as _e:
            # Non-fatal: interface availability can vary during startup
            pass

        self.info_bar_service.publish_message(status_msg)

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        global_event_bus.publish(Events.REQUEST_VIEW_TRANSITION.name, target_view)

    def _handle_add_files_request(self):
        """Handle request to add files from file pane (deprecated direct call).
        
        Event-first architecture: This method should not be invoked anymore.
        UDFileView emits add_files_requested -> UpdateDataView translates to
        Local Bus SOURCE_SELECT_REQUESTED with SELECT_FILES. FileManager handles it.
        """
        try:
            from ._ui.option_types import SourceOptions
            # Publish the canonical intent on the local event bus instead of calling dialogs directly
            self.local_bus.emit(ViewEvents.SOURCE_SELECT_REQUESTED.value, SourceOptions.SELECT_FILES)
            log.debug("[PRESENTER] Redirected add files request to Local Bus (SOURCE_SELECT_REQUESTED:SELECT_FILES)")
        except Exception as e:
            log.error(f"Error redirecting add files request to Local Bus: {e}")

    # Removed: _on_file_list_updated. View updates file display directly from FILE_LIST_UPDATED.

    def cleanup(self):
        """Clean up before being replaced."""
        # Unsubscribe from events using processing manager
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self.processing_manager.on_processing_started
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self.processing_manager.on_processing_stats
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()

    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass
            