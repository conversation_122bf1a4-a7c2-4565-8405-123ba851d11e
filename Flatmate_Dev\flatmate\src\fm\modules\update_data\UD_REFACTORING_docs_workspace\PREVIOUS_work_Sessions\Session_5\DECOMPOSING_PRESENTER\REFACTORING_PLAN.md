# 📈 Refactoring Plan: UpdateDataPresenter Decomposition

This document outlines the phased approach for decomposing the `UpdateDataPresenter`. Each phase represents a self-contained unit of work that will be completed in a single session or sprint, concluding with testing and review.

## Guiding Principles

- **Incremental Changes:** Each phase will introduce a small, logical change.
- **Test After Each Phase:** Full regression testing will be performed at the end of each phase.
- **Stable Master:** The application must be in a working, stable state upon completion of each phase.

---

## Phase 1: Project Setup & State Extraction

*   **Goal:** Create the new package structure and extract the `UpdateDataState` class into its own module. This is the foundational step with no logic changes.
*   **Tasks:** See `TASK_BACKLOG.md` (Tasks 1.1 - 1.7)
*   **Definition of Done:**
    *   The new `presenter` directory and manager files are created.
    *   `UpdateDataState` class lives in `state_manager.py`.
    *   The main presenter imports the state class from the new location.
    *   The application runs correctly.
    *   All existing tests pass without any changes.

## Phase 2: Widget State Manager Extraction

*   **Goal:** Extract all methods responsible for synchronizing state with the UI into a dedicated `WidgetStateManager`.
*   **Tasks:** See `TASK_BACKLOG.md`
*   **Definition of Done:**
    *   `WidgetStateManager` is implemented and handles all direct view updates.
    *   `UpdateDataPresenter` delegates UI updates to the `WidgetStateManager`.
    *   The application runs correctly, and the UI reflects the state as before.
    *   All existing tests pass.

## Phase 3: Source Manager Extraction

*   **Goal:** Extract all logic related to source selection (files/folders) and folder monitoring.
*   **Tasks:** See `TASK_BACKLOG.md`
*   **Definition of Done:**
    *   `SourceManager` is implemented and handles all source selection logic.
    *   `UpdateDataPresenter` delegates source-related actions to the `SourceManager`.
    *   Selecting files, selecting a folder, and folder monitoring all function correctly.
    *   All existing tests pass.

## Phase 4: Archive & Processing Manager Extraction

*   **Goal:** Extract the remaining logic for destination selection and file processing into their respective managers.
*   **Tasks:** See `TASK_BACKLOG.md`
*   **Definition of Done:**
    *   `ArchiveManager` and `ProcessingManager` are implemented.
    *   The main presenter is now a lean coordinator, delegating all major tasks.
    *   The entire "Update Data" workflow functions end-to-end as before.
    *   All existing tests pass.

## Phase 5: Final Review and Cleanup

*   **Goal:** Review the new architecture, remove dead code, and add documentation.
*   **Tasks:** See `TASK_BACKLOG.md`
*   **Definition of Done:**
    *   Code is cleaned and commented.
    *   The refactoring is officially complete.
