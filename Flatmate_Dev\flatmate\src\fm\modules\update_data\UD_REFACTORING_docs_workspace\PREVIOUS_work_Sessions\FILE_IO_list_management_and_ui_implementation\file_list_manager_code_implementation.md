# FileListManager - Complete Code Implementation

## 1. FileListManager Class

```python
# _presenter/file_list_manager.py

"""
File list management for Update Data module.

Handles canonical file list, folder monitoring integration, and file operations.
Separated from FileManager to provide clear separation of concerns.
"""

import os
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

from ....core.services.logger import log
from ..services.events_data import FileListUpdatedEvent, FileAddedEvent, FileRemovedEvent
from ..services.local_event_bus import ViewEvents


@dataclass
class MonitoredFolder:
    """Simple dataclass for folder monitoring status as requested by user."""
    path: str
    monitor_new_files: bool = False
    last_scan: Optional[datetime] = None
    file_count: int = 0


class FileListManager:
    """
    Manages the canonical file list and folder monitoring integration.
    
    Responsibilities:
    - Owns canonical file_paths_list (moved from FileManager)
    - Manages monitored folders list
    - Handles file add/remove operations
    - Integrates with existing FolderMonitorService
    - Emits events for UI updates
    """
    
    def __init__(self, folder_monitor_service, local_bus, file_info_service):
        """
        Initialize the file list manager.
        
        Args:
            folder_monitor_service: Core folder monitoring service
            local_bus: Local event bus for module communication
            file_info_service: Service for file enrichment
        """
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.file_info_service = file_info_service
        
        # Canonical file list - moved from FileManager
        self.file_paths_list: List[str] = []
        
        # Monitored folders - simple dict as user suggested
        self.monitored_folders: Dict[str, MonitoredFolder] = {}
        
        # Subscribe to relevant events
        self._subscribe_to_events()
        
        log.debug("[FILE_LIST_MANAGER] Initialized with empty file list")
    
    def _subscribe_to_events(self):
        """Subscribe to events that affect file list management."""
        # Subscribe to folder monitoring events
        self.local_bus.subscribe(ViewEvents.FOLDER_MONITORING_TOGGLED.value, 
                               self._on_folder_monitoring_toggled)
        
        # Subscribe to file discovery events from folder monitoring
        self.local_bus.subscribe(ViewEvents.FILES_DISCOVERED.value, 
                               self._on_files_discovered)
    
    def set_files(self, file_paths: List[str], source_path: str = ""):
        """
        Set the canonical file list.
        
        Args:
            file_paths: List of file paths to set
            source_path: Source directory path (if applicable)
        """
        try:
            # Store canonical list
            self.file_paths_list = file_paths.copy() if file_paths else []
            
            log.debug(f"[FILE_LIST_MANAGER] Set file list: {len(self.file_paths_list)} files")
            
            # Update monitored folder if source is a directory
            if source_path and os.path.isdir(source_path):
                self._update_monitored_folder(source_path, len(file_paths))
            
            # Emit event for UI updates
            self._emit_list_updated(source_path)
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error setting files: {e}")
    
    def add_files(self, new_files: List[str]) -> bool:
        """
        Add files to the canonical list.
        
        Args:
            new_files: List of file paths to add
            
        Returns:
            bool: True if files were added, False otherwise
        """
        try:
            if not new_files:
                return False
            
            # Avoid duplicates
            unique_files = [f for f in new_files if f not in self.file_paths_list]
            
            if unique_files:
                self.file_paths_list.extend(unique_files)
                self._emit_list_updated()
                
                # Emit individual file added events
                for file_path in unique_files:
                    self.local_bus.emit(ViewEvents.FILE_ADDED.value, 
                                      FileAddedEvent(file_path=file_path))
                
                log.debug(f"[FILE_LIST_MANAGER] Added {len(unique_files)} files to list")
                return True
            else:
                log.debug("[FILE_LIST_MANAGER] No new files to add (duplicates filtered)")
                return False
                
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error adding files: {e}")
            return False
    
    def remove_file(self, file_path: str) -> bool:
        """
        Remove file from the canonical list.
        
        Args:
            file_path: Path of file to remove
            
        Returns:
            bool: True if file was removed, False otherwise
        """
        try:
            if file_path in self.file_paths_list:
                self.file_paths_list.remove(file_path)
                self._emit_list_updated()
                
                # Emit file removed event
                self.local_bus.emit(ViewEvents.FILE_REMOVED.value, 
                                  FileRemovedEvent(file_path=file_path))
                
                log.debug(f"[FILE_LIST_MANAGER] Removed file: {os.path.basename(file_path)}")
                return True
            else:
                log.warning(f"[FILE_LIST_MANAGER] File not in list: {file_path}")
                return False
                
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error removing file: {e}")
            return False
    
    def get_files(self) -> List[str]:
        """
        Get copy of canonical file list.
        
        Returns:
            List[str]: Copy of current file list
        """
        return self.file_paths_list.copy()
    
    def get_file_count(self) -> int:
        """Get current file count."""
        return len(self.file_paths_list)
    
    def clear_files(self):
        """Clear the file list."""
        self.file_paths_list.clear()
        self._emit_list_updated()
        log.debug("[FILE_LIST_MANAGER] Cleared file list")
    
    def set_folder_monitoring(self, folder_path: str, enabled: bool):
        """
        Set monitoring status for a folder.
        
        Args:
            folder_path: Path to folder
            enabled: Whether monitoring should be enabled
        """
        try:
            folder_path = str(Path(folder_path).resolve())
            
            if folder_path in self.monitored_folders:
                self.monitored_folders[folder_path].monitor_new_files = enabled
            else:
                self.monitored_folders[folder_path] = MonitoredFolder(
                    path=folder_path, 
                    monitor_new_files=enabled,
                    last_scan=datetime.now() if enabled else None
                )
            
            # Update FolderMonitorService
            self.folder_monitor_service.set_folder_monitored(folder_path, enabled)
            
            log.debug(f"[FILE_LIST_MANAGER] Folder monitoring {'enabled' if enabled else 'disabled'} for: {folder_path}")
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error setting folder monitoring: {e}")
    
    def get_monitored_folders(self) -> Dict[str, MonitoredFolder]:
        """Get copy of monitored folders dict."""
        return self.monitored_folders.copy()
    
    def is_folder_monitored(self, folder_path: str) -> bool:
        """Check if a folder is being monitored."""
        folder_path = str(Path(folder_path).resolve())
        folder_info = self.monitored_folders.get(folder_path)
        return folder_info.monitor_new_files if folder_info else False
    
    def _update_monitored_folder(self, folder_path: str, file_count: int):
        """Update monitored folder information."""
        try:
            folder_path = str(Path(folder_path).resolve())
            
            if folder_path in self.monitored_folders:
                self.monitored_folders[folder_path].file_count = file_count
                self.monitored_folders[folder_path].last_scan = datetime.now()
            else:
                # Add as known folder but not monitored by default
                self.monitored_folders[folder_path] = MonitoredFolder(
                    path=folder_path,
                    monitor_new_files=False,
                    last_scan=datetime.now(),
                    file_count=file_count
                )
            
            log.debug(f"[FILE_LIST_MANAGER] Updated folder info: {folder_path} ({file_count} files)")
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error updating monitored folder: {e}")
    
    def _emit_list_updated(self, source_path: str = ""):
        """Emit file list updated event."""
        try:
            event_data = FileListUpdatedEvent(
                files=self.file_paths_list.copy(),
                source_path=source_path
            )
            self.local_bus.emit(ViewEvents.FILE_LIST_UPDATED.value, event_data)
            log.debug(f"[FILE_LIST_MANAGER] Emitted FILE_LIST_UPDATED event ({len(self.file_paths_list)} files)")
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error emitting list updated event: {e}")
    
    def _on_folder_monitoring_toggled(self, event_data):
        """Handle folder monitoring toggle events."""
        try:
            if hasattr(event_data, 'folder_path') and hasattr(event_data, 'enabled'):
                self.set_folder_monitoring(event_data.folder_path, event_data.enabled)
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error handling folder monitoring toggle: {e}")
    
    def _on_files_discovered(self, event_data):
        """Handle files discovered from folder monitoring."""
        try:
            if hasattr(event_data, 'files') and event_data.files:
                # Add discovered files to the list
                self.add_files(event_data.files)
                log.debug(f"[FILE_LIST_MANAGER] Added {len(event_data.files)} discovered files")
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error handling discovered files: {e}")
```

## 2. Event Definitions Update

```python
# services/events_data.py - Add these new events

@dataclass
class FileListUpdatedEvent:
    """Event data for file list updates."""
    files: List[str]
    source_path: str = ""

@dataclass
class FileAddedEvent:
    """Event data for individual file addition."""
    file_path: str

@dataclass
class FileRemovedEvent:
    """Event data for individual file removal."""
    file_path: str

@dataclass
class FolderMonitoringToggledEvent:
    """Event data for folder monitoring changes."""
    folder_path: str
    enabled: bool

@dataclass
class FilesDiscoveredEvent:
    """Event data for files discovered via monitoring."""
    files: List[str]
    source_folder: str
```

## 3. ViewEvents Enum Update

```python
# services/events.py - Add new event types

class ViewEvents(Enum):
    # ... existing events ...
    
    # File list management events
    FILE_LIST_UPDATED = "file_list_updated"
    FILE_ADDED = "file_added"
    FILE_REMOVED = "file_removed"
    FOLDER_MONITORING_TOGGLED = "folder_monitoring_toggled"
    FILES_DISCOVERED = "files_discovered"
```

This implementation provides:

1. **Clear ownership** of the canonical file list
2. **Simple dataclass** for monitored folders as requested
3. **Event-driven communication** for UI updates
4. **Integration** with existing FolderMonitorService
5. **Robust error handling** for add/remove operations
6. **Focused responsibility** - just file list management

The FileListManager can be integrated into the existing presenter architecture through dependency injection, maintaining the current MVP pattern while providing better separation of concerns.
