# Protocol: K2 Sequential Reasoning Primer

**Version**: 1.0  
**Status**: Active  
**Author**: Cascade & Quinn  
**Tool**: Uses the sequential-thinking MCP tool

---

## 1. Objective

To enforce a methodical, sequential reasoning process that prevents rushing to conclusions and ensures thorough problem-solving. This protocol leverages the sequential-thinking tool to formalize step-by-step reasoning, allowing for revision, branching, and explicit tracking of the thought process. It is designed to counteract the tendency to skip steps in reasoning or jump to implementation without proper analysis.

## 2. Core Principles

1. **Sequential Thought**: Break down complex problems into clear, logical steps with explicit numbering.
2. **Evidence-Based Reasoning**: Every conclusion must be supported by specific evidence.
3. **Explicit State Tracking**: Maintain awareness of what is known, unknown, and assumed.
4. **Hypothesis Testing**: Test assumptions before accepting them as facts.
5. **Revision Capability**: Ability to revisit and revise previous reasoning steps when new information emerges.
6. **Branching Logic**: Explore alternative reasoning paths when appropriate.
7. **Completeness Check**: Verify all aspects of a problem have been addressed.
8. **Thought Estimation**: Provide an initial estimate of required thinking steps and adjust as needed.

## 3. Sequential-Thinking Tool Integration

This protocol uses the sequential-thinking tool which requires the following parameters:

* **thought**: The current thinking step being articulated
* **thoughtNumber**: Position in the sequence of thoughts
* **totalThoughts**: Estimated total thoughts needed (adjustable)
* **nextThoughtNeeded**: Boolean indicating if more thinking is required
* **isRevision**: Boolean indicating if this thought revises previous thinking
* **revisesThought**: Which thought number is being reconsidered (if applicable)
* **branchFromThought**: Which thought is the branching point (if applicable)
* **branchId**: Identifier for the current branch (if applicable)

## 4. Reasoning Framework

### Step 4.1: Problem Definition

Before attempting to solve any problem:

* **Explicit Statement**: Clearly articulate what problem needs solving.
* **Scope Definition**: Define the boundaries of the problem space.
* **Success Criteria**: Establish how to determine when the problem is solved.

```
PROBLEM STATEMENT:
[Concise description of the problem]

SCOPE:
- [What's included]
- [What's excluded]

SUCCESS CRITERIA:
- [Measurable outcomes]
```

### Step 4.2: Knowledge Inventory

Take stock of what is known and unknown:

* **Known Facts**: List verified information relevant to the problem.
* **Knowledge Gaps**: Identify missing information critical to solving the problem.
* **Assumptions**: Explicitly state any assumptions being made.

```
KNOWN FACTS:
- [Fact 1]
- [Fact 2]

KNOWLEDGE GAPS:
- [Gap 1]
- [Gap 2]

ASSUMPTIONS:
- [Assumption 1]
- [Assumption 2]
```

### Step 4.3: Investigation Plan

Create a structured approach to fill knowledge gaps:

* **Research Questions**: Formulate specific questions to answer.
* **Information Sources**: Identify where to find needed information.
* **Investigation Methods**: Decide how to extract relevant information.

```
RESEARCH QUESTIONS:
1. [Question 1]
2. [Question 2]

INFORMATION SOURCES:
- [Source 1]
- [Source 2]

INVESTIGATION METHODS:
- [Method 1]
- [Method 2]
```

### Step 4.4: Sequential Analysis

Process information in a structured manner:

* **Step-by-Step Reasoning**: Document each logical step in the analysis.
* **Evidence Linking**: Connect each conclusion to specific evidence.
* **Alternative Explanations**: Consider multiple interpretations of the evidence.

```
SEQUENTIAL THINKING PROCESS:

thought: "[Initial observation/fact]"
thoughtNumber: 1
totalThoughts: [initial estimate, e.g., 5]
nextThoughtNeeded: true
isRevision: false

thought: "[Logical step based on #1]"
thoughtNumber: 2
totalThoughts: [adjusted if needed]
nextThoughtNeeded: true
isRevision: false

thought: "[Logical step based on #2]"
thoughtNumber: 3
totalThoughts: [adjusted if needed]
nextThoughtNeeded: true
isRevision: false

# Example of revision:
thought: "[New information contradicts thought #2, so revising that conclusion]"
thoughtNumber: 4
totalThoughts: [adjusted if needed]
nextThoughtNeeded: true
isRevision: true
revisesThought: 2

# Example of branching:
thought: "[Alternative reasoning path from thought #1]"
thoughtNumber: 5
totalThoughts: [adjusted if needed]
nextThoughtNeeded: true
isRevision: false
branchFromThought: 1
branchId: "alternative-path"

EVIDENCE LINKS:
- Conclusion X is supported by [specific evidence]
- Conclusion Y is supported by [specific evidence]
```

### Step 4.5: Solution Formulation

Develop solutions based on the analysis:

* **Solution Options**: Generate multiple possible solutions.
* **Evaluation Criteria**: Define how to assess solution quality.
* **Trade-off Analysis**: Compare solutions against criteria.

```
SOLUTION OPTIONS:
1. [Option 1]
2. [Option 2]

EVALUATION CRITERIA:
- [Criterion 1]
- [Criterion 2]

TRADE-OFF ANALYSIS:
- Option 1: [Strengths] / [Weaknesses]
- Option 2: [Strengths] / [Weaknesses]
```

### Step 4.6: Implementation Planning

Plan the execution of the chosen solution:

* **Action Steps**: Break down implementation into discrete tasks.
* **Dependencies**: Identify prerequisites for each step.
* **Validation Points**: Define checkpoints to verify progress.

```
ACTION STEPS:
1. [Step 1]
2. [Step 2]

DEPENDENCIES:
- Step 2 requires completion of Step 1
- Step 3 requires [external resource]

VALIDATION POINTS:
- After Step 1: [Expected outcome]
- After Step 2: [Expected outcome]
```

### Step 4.7: Completeness Verification

Before finalizing:

* **Requirement Check**: Verify all requirements have been addressed.
* **Edge Case Analysis**: Consider boundary conditions and exceptions.
* **Assumption Validation**: Confirm or update initial assumptions.

```
REQUIREMENTS COVERAGE:
- [Requirement 1]: [How addressed]
- [Requirement 2]: [How addressed]

EDGE CASES CONSIDERED:
- [Edge case 1]: [How handled]
- [Edge case 2]: [How handled]

ASSUMPTION UPDATES:
- [Initial assumption 1] → [Current status]
- [Initial assumption 2] → [Current status]
```

## 5. Implementation Guidelines

1. **Tool Usage**: Always use the sequential-thinking tool for complex reasoning tasks.
2. **Template Usage**: Use the provided templates for each reasoning step.
3. **Explicit Transitions**: Clearly mark transitions between reasoning stages.
4. **Reasoning Artifacts**: Preserve intermediate reasoning products.
5. **Backtracking**: When new information invalidates previous conclusions, use the isRevision parameter to explicitly update the reasoning chain.
6. **Branching**: When exploring alternative paths, use the branchFromThought parameter to clearly indicate the divergence point.
7. **Thought Estimation**: Start with a reasonable estimate of totalThoughts and adjust as you progress.
8. **Meta-Cognition**: Periodically assess the quality of the reasoning process itself.
9. **Completion Indicator**: Only set nextThoughtNeeded to false when truly done and a satisfactory answer is reached.

## 6. Application Contexts

This protocol should be applied in these situations:

* Complex problem-solving requiring multiple logical steps
* Debugging issues with unclear root causes
* Design decisions with significant architectural impact
* Risk assessment and mitigation planning
* Performance optimization tasks

---

## 7. Usage Example

```
# Step 4.1: Problem Definition
PROBLEM STATEMENT:
The application crashes when processing large CSV files.

SCOPE:
- CSV processing module
- Memory management
- Not addressing UI responsiveness

SUCCESS CRITERIA:
- Process 10MB CSV files without crashes
- Memory usage stays below 500MB

# Step 4.4: Sequential Analysis (using the sequential-thinking tool)

thought: "The application crashes when processing large CSV files, suggesting a resource limitation issue."
thoughtNumber: 1
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

thought: "Resource limitations could be memory-related (heap overflow), CPU-related (processing bottleneck), or I/O-related (file handling issues)."
thoughtNumber: 2
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

thought: "Looking at the error logs, I see 'OutOfMemoryError' exceptions occurring when the file size exceeds 5MB, pointing to memory as the primary constraint."
thoughtNumber: 3
totalThoughts: 6  # Adjusted as more steps are needed
nextThoughtNeeded: true
isRevision: false

thought: "The current implementation loads the entire CSV into memory at once using pandas.read_csv() without any chunking mechanism."
thoughtNumber: 4
totalThoughts: 6
nextThoughtNeeded: true
isRevision: false

thought: "Wait, reviewing the code more carefully, I see there is actually a chunking mechanism in place, but the chunk size is hardcoded to a value that's too large (1,000,000 rows)."
thoughtNumber: 5
totalThoughts: 6
nextThoughtNeeded: true
isRevision: true
revisesThought: 4

thought: "Solution: Modify the CSV processing module to use a smaller, configurable chunk size based on available system memory and file size."
thoughtNumber: 6
totalThoughts: 6
nextThoughtNeeded: false
isRevision: false
- Memory usage stays below 500MB
```

[Continue with remaining steps from the framework...]
