#!/usr/bin/env python3
"""
Test script to verify the file selector implementation works correctly.
This tests the new unified API without requiring the full application.
"""

import sys
import os
from pathlib import Path

# Add the flatmate src to path
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

def test_file_selector_import():
    """Test that FileSelector can be imported and has the new unified API."""
    print("=== Testing FileSelector Import ===")
    
    try:
        from fm.modules.update_data._ui._view.shared_components.file_selector import FileSelector, FileUtils
        print("✅ Successfully imported FileSelector and FileUtils")
        
        # Check if the new get_paths method exists
        if hasattr(FileSelector, 'get_paths'):
            print("✅ FileSelector.get_paths() method exists")
        else:
            print("❌ FileSelector.get_paths() method missing")
            return False
            
        # Check if it's a static method
        if callable(getattr(FileSelector, 'get_paths')):
            print("✅ FileSelector.get_paths() is callable")
        else:
            print("❌ FileSelector.get_paths() is not callable")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_file_selector_api():
    """Test the FileSelector API without GUI dialogs."""
    print("\n=== Testing FileSelector API ===")
    
    try:
        from fm.modules.update_data._ui._view.shared_components.file_selector import FileSelector
        
        # Test invalid selection type
        try:
            result = FileSelector.get_paths('invalid_type')
            print("❌ Should have raised ValueError for invalid selection type")
            return False
        except ValueError as e:
            print(f"✅ Correctly raised ValueError for invalid selection type: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
            
        print("✅ FileSelector API validation passed")
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_file_manager_import():
    """Test that FileManager can be imported and has the updated methods."""
    print("\n=== Testing FileManager Import ===")
    
    try:
        from fm.modules.update_data._ui._presenter.file_management import FileManager
        print("✅ Successfully imported FileManager")
        
        # Check if the new _process_selected_files method exists
        if hasattr(FileManager, '_process_selected_files'):
            print("✅ FileManager._process_selected_files() method exists")
        else:
            print("❌ FileManager._process_selected_files() method missing")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_file_utils():
    """Test FileUtils functionality."""
    print("\n=== Testing FileUtils ===")
    
    try:
        from fm.modules.update_data._ui._view.shared_components.file_selector import FileUtils
        
        # Test discover_files_in_folder with non-existent path
        result = FileUtils.discover_files_in_folder("/non/existent/path")
        if result == []:
            print("✅ FileUtils.discover_files_in_folder() handles invalid path correctly")
        else:
            print(f"❌ Expected empty list for invalid path, got: {result}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ FileUtils test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("File Selector Implementation Test Suite")
    print("=" * 50)
    
    tests = [
        test_file_selector_import,
        test_file_selector_api,
        test_file_manager_import,
        test_file_utils
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
