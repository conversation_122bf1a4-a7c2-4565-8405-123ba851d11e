"""
Service for analyzing files and providing enriched file information.
"""
import os
from typing import List, Dict, Any

from ..pipeline.statement_handlers._handler_registry import get_handler
from ....core.services.logger import log

class FileInfoService:
    """Service to analyze files and determine their properties."""

    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Extract file information using handler detection.

        Args:
            file_path: Path to the file.

        Returns:
            A dictionary with file information including size and format.
        """
        normalized_path = os.path.normpath(file_path)

        if not os.path.exists(normalized_path):
            raise FileNotFoundError(f"File not found at {normalized_path}")

        size = os.path.getsize(normalized_path)
        size_str = (
            f"{size / 1024:.1f} KB"
            if size < 1024 * 1024
            else f"{size / (1024 * 1024):.1f} MB"
        )

        handler_instance = get_handler(normalized_path)

        format_info = {
            "bank_type": "Unknown",
            "format_type": "Unrecognized",
            "handler": None,
        }

        if handler_instance:
            format_info = {
                "bank_type": handler_instance.__class__.statement_type.bank_name,
                "format_type": handler_instance.__class__.statement_type.variant,
                "handler": handler_instance.__class__.__name__,
            }

        return {
            "path": normalized_path,
            "size_bytes": size,
            "size_str": size_str,
            **format_info,
        }

    @staticmethod
    def discover_files(file_paths: List[str]) -> List[Dict[str, Any]]:
        """
        Process a list of files and collect their information.

        Args:
            file_paths: List of file paths to process.

        Returns:
            A list of dictionaries, each containing info for a file.
        """
        return [FileInfoService.get_file_info(fp) for fp in file_paths]
