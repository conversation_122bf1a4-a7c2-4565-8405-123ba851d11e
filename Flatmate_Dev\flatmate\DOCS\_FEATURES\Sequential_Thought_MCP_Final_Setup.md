# Sequential Thought MCP Server: Final Setup Guide

## Overview
This is the definitive guide for setting up and managing the Sequential Thought MCP server on Windows 10 using PM2.

## Current Setup

The Sequential Thought MCP server is:
- **Running via PM2** as `sequential-thought-mcp`
- **Managed by scripts** in `~/_scripts/` directory
- **Connected to Windsurf** via external server configuration

## Script Locations

| File | Path | Purpose |
|------|------|---------|
| `start-sequential-thought.js` | `~/_scripts/start-sequential-thought.js` | Main launcher script |
| `sequential-thought.config.js` | `~/_scripts/sequential-thought.config.js` | PM2 configuration |
| `check-and-start-sequential-thought.js` | `~/_scripts/check-and-start-sequential-thought.js` | Auto-start script |

## Windsurf Configuration

Windsurf is configured to connect to the externally running PM2-managed server:

```json
{
  "servers": [
    {
      "name": "sequential-thought",
      "endpoint": "http://localhost:4000",
      "type": "node",
      "description": "Sequential Thought MCP server running locally"
    }
  ]
}
```

## Managing the Server

### Check Status
```bash
pm2 status
```

### View Logs
```bash
pm2 logs sequential-thought-mcp
```

### Restart Server
```bash
pm2 restart sequential-thought-mcp
```

### Stop Server
```bash
pm2 stop sequential-thought-mcp
```

### Start Server (if stopped)
```bash
pm2 start ~/_scripts/sequential-thought.config.js
```

## Auto-start Configuration

### PM2 Startup
To ensure the server starts on system boot:
```bash
pm2 save
pm2 startup
```

### Windsurf Integration
To check if the server is running when Windsurf starts:
```bash
node ~/_scripts/check-and-start-sequential-thought.js
```

## Shell Aliases (Optional)

Add to your `~/.bashrc`:
```bash
# Sequential Thought MCP server management
alias st-start="pm2 start ~/_scripts/sequential-thought.config.js"
alias st-stop="pm2 stop sequential-thought-mcp"
alias st-restart="pm2 restart sequential-thought-mcp"
alias st-status="pm2 status sequential-thought-mcp"
alias st-logs="pm2 logs sequential-thought-mcp"
alias st-check="node ~/_scripts/check-and-start-sequential-thought.js"
```
