#!/usr/bin/env python3
"""
Folder Monitor Service

Monitors designated folders for new files and notifies registered callbacks.
Files are not automatically processed, only discovered and added to the UI.
"""

import os
import queue
import threading
import time
from pathlib import Path
from typing import Dict, List, Callable, Optional, Any, Set

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from fm.core.config import config
from fm.core.services.logger import log
from fm.modules.update_data.config.ud_config import ud_config
from fm.modules.update_data.config.ud_keys import UpdateDataKeys


class FolderMonitorEventHandler(FileSystemEventHandler):
    """Handle file system events for monitored folders"""
    
    def __init__(self, service, folder_path: str):
        super().__init__()
        self.service = service
        self.folder_path = folder_path
    
    def on_created(self, event):
        """Handle new file creation events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        allowed_extensions = ud_config.get_value(
            UpdateDataKeys.FolderMonitoring.ALLOWED_EXTENSIONS, 
            default=['.csv']
        )
        
        if file_path.suffix.lower() in allowed_extensions:
            log.info(f"New file detected: {file_path.name} in {self.folder_path}")
            self.service.queue_file_for_discovery(str(file_path), self.folder_path)
    
    def on_modified(self, event):
        """Handle file modification events (for large files)"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        allowed_extensions = ud_config.get_value(
            UpdateDataKeys.FolderMonitoring.ALLOWED_EXTENSIONS, 
            default=['.csv']
        )
        
        if file_path.suffix.lower() in allowed_extensions:
            log.debug(f"File modified: {file_path.name} in {self.folder_path}")
            self.service.queue_file_for_discovery(str(file_path), self.folder_path)


class FolderMonitorService:
    """Service for monitoring folders to discover new files
    
    Features:
    - Monitor multiple folders simultaneously
    - Per-folder monitoring status
    - Callback registration for file discovery notifications
    - Configurable via update_data config system
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, 'initialized'):
            return
        
        self.initialized = True
        self.observers: Dict[str, Observer] = {}
        self.processing_queue = queue.Queue(maxsize=100)
        self.worker_thread = None
        self.running = False
        self.callbacks: List[Callable[[str, str], None]] = []
        
        # Known folders with monitoring status
        self.known_folders: List[Dict[str, Any]] = []
        
        # Load configuration
        self._load_configuration()
        
        log.info(f"FolderMonitorService initialized")
    
    def _load_configuration(self) -> None:
        """Load folder monitoring configuration from settings"""
        # Load known folders with their monitoring status
        self.known_folders = ud_config.get_value(
            UpdateDataKeys.FolderMonitoring.KNOWN_FOLDERS, 
            default=[]
        )
        
        # Start monitoring for folders that are marked as monitored
        for folder_info in self.known_folders:
            if folder_info.get('monitored', False):
                folder_path = folder_info.get('path')
                if folder_path and Path(folder_path).exists():
                    self._start_monitoring_folder(folder_path)
    
    def _save_configuration(self) -> None:
        """Save folder monitoring configuration to settings"""
        # Ensure we don't exceed the max number of known folders
        max_known_folders = ud_config.get_value(
            UpdateDataKeys.FolderMonitoring.MAX_KNOWN_FOLDERS, 
            default=5
        )
        
        if len(self.known_folders) > max_known_folders:
            # Keep only the most recent ones
            self.known_folders = self.known_folders[-max_known_folders:]
        
        # Save to config
        ud_config.set_value(
            UpdateDataKeys.FolderMonitoring.KNOWN_FOLDERS, 
            self.known_folders
        )
    
    def register_callback(self, callback: Callable[[str, str], None]) -> None:
        """Register a callback to be notified when files are discovered
        
        Args:
            callback: Function that takes file_path and source_folder as arguments
        """
        if callback not in self.callbacks:
            self.callbacks.append(callback)
            log.debug(f"Registered file discovery callback: {callback.__qualname__}")
    
    def unregister_callback(self, callback: Callable[[str, str], None]) -> None:
        """Unregister a previously registered callback
        
        Args:
            callback: The callback function to unregister
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            log.debug(f"Unregistered file discovery callback: {callback.__qualname__}")
    
    def add_known_folder(self, folder_path: str, monitored: bool = False) -> None:
        """Add a folder to the known folders list
        
        Args:
            folder_path: Path to the folder
            monitored: Whether the folder should be monitored
        """
        folder_path = str(Path(folder_path).resolve())
        
        # Check if folder is already in the list
        for folder_info in self.known_folders:
            if folder_info.get('path') == folder_path:
                # Update monitored status if it changed
                if folder_info.get('monitored') != monitored:
                    folder_info['monitored'] = monitored
                    self._save_configuration()
                    
                    if monitored:
                        self._start_monitoring_folder(folder_path)
                    else:
                        self._stop_monitoring_folder(folder_path)
                
                return
        
        # Add new folder to the list
        self.known_folders.append({
            'path': folder_path,
            'monitored': monitored,
            'added_timestamp': time.time()
        })
        
        # Start monitoring if requested
        if monitored:
            self._start_monitoring_folder(folder_path)
        
        # Save configuration
        self._save_configuration()
        
        log.info(f"Added folder to known folders: {folder_path} (monitored: {monitored})")
    
    def set_folder_monitored(self, folder_path: str, monitored: bool = True) -> None:
        """Set the monitoring status for a folder
        
        Args:
            folder_path: Path to the folder
            monitored: Whether the folder should be monitored
        """
        folder_path = str(Path(folder_path).resolve())
        
        # Check if folder is in the known folders list
        for folder_info in self.known_folders:
            if folder_info.get('path') == folder_path:
                # Update monitored status if it changed
                if folder_info.get('monitored') != monitored:
                    folder_info['monitored'] = monitored
                    self._save_configuration()
                    
                    if monitored:
                        self._start_monitoring_folder(folder_path)
                    else:
                        self._stop_monitoring_folder(folder_path)
                
                log.info(f"Updated monitoring status for folder: {folder_path} (monitored: {monitored})")
                return
        
        # If folder is not in the list, add it
        self.add_known_folder(folder_path, monitored)
    
    def is_folder_monitored(self, folder_path: str) -> bool:
        """Check if a folder is currently being monitored
        
        Args:
            folder_path: Path to the folder
            
        Returns:
            bool: True if the folder is being monitored, False otherwise
        """
        folder_path = str(Path(folder_path).resolve())
        
        for folder_info in self.known_folders:
            if folder_info.get('path') == folder_path:
                return folder_info.get('monitored', False)
        
        return False
    
    def get_known_folders(self) -> List[Dict[str, Any]]:
        """Get the list of known folders with their monitoring status
        
        Returns:
            List of dictionaries with folder information
        """
        return self.known_folders.copy()
    
    def start(self) -> None:
        """Start the file discovery worker thread"""
        if self.running:
            log.warning("Folder monitor service already running")
            return
        
        try:
            self.running = True
            
            # Start worker thread for processing discovered files
            self.worker_thread = threading.Thread(
                target=self._process_files_worker,
                daemon=True,
                name="FolderMonitorWorker"
            )
            self.worker_thread.start()
            
            # Start monitoring all folders marked as monitored
            for folder_info in self.known_folders:
                if folder_info.get('monitored', False):
                    folder_path = folder_info.get('path')
                    if folder_path and Path(folder_path).exists():
                        self._start_monitoring_folder(folder_path)
            
            log.info("Folder monitor service started")
            
        except Exception as e:
            self.running = False
            log.error(f"Failed to start folder monitor service: {e}")
    
    def stop(self) -> None:
        """Stop monitoring and cleanup resources"""
        if not self.running:
            log.debug("Folder monitor service not running")
            return
        
        log.info("Stopping folder monitor service...")
        self.running = False
        
        # Stop all observers
        for folder_path, observer in self.observers.items():
            try:
                observer.stop()
                observer.join(timeout=2.0)
                log.debug(f"Stopped monitoring folder: {folder_path}")
            except Exception as e:
                log.error(f"Error stopping observer for {folder_path}: {e}")
        
        self.observers.clear()
        
        # Stop worker thread
        if self.worker_thread and self.worker_thread.is_alive():
            # Signal worker to exit
            try:
                self.processing_queue.put(None, block=False)  # Shutdown signal
            except queue.Full:
                pass
            
            self.worker_thread.join(timeout=5.0)
        
        log.info("Folder monitor service stopped")
    
    def _start_monitoring_folder(self, folder_path: str) -> None:
        """Start monitoring a specific folder
        
        Args:
            folder_path: Path to the folder to monitor
        """
        folder_path = str(Path(folder_path).resolve())
        
        # Check if folder exists
        if not Path(folder_path).exists():
            log.error(f"Cannot monitor non-existent folder: {folder_path}")
            return
        
        # Check if already monitoring this folder
        if folder_path in self.observers:
            log.debug(f"Already monitoring folder: {folder_path}")
            return
        
        try:
            # Create and start observer for this folder
            observer = Observer()
            event_handler = FolderMonitorEventHandler(self, folder_path)
            observer.schedule(event_handler, folder_path, recursive=False)
            observer.start()
            
            # Store observer
            self.observers[folder_path] = observer
            
            log.info(f"Started monitoring folder: {folder_path}")
            
        except Exception as e:
            log.error(f"Failed to start monitoring folder {folder_path}: {e}")
    
    def _stop_monitoring_folder(self, folder_path: str) -> None:
        """Stop monitoring a specific folder
        
        Args:
            folder_path: Path to the folder to stop monitoring
        """
        folder_path = str(Path(folder_path).resolve())
        
        # Check if we're monitoring this folder
        if folder_path not in self.observers:
            log.debug(f"Not monitoring folder: {folder_path}")
            return
        
        try:
            # Stop and remove observer
            observer = self.observers.pop(folder_path)
            observer.stop()
            observer.join(timeout=2.0)
            
            log.info(f"Stopped monitoring folder: {folder_path}")
            
        except Exception as e:
            log.error(f"Error stopping monitoring for folder {folder_path}: {e}")
    
    def queue_file_for_discovery(self, file_path: str, source_folder: str) -> None:
        """Add file to discovery queue
        
        Args:
            file_path: Path to the file
            source_folder: Path to the folder where the file was discovered
        """
        try:
            self.processing_queue.put((file_path, source_folder), block=False)
            log.debug(f"Queued for discovery: {Path(file_path).name}")
        except queue.Full:
            log.error(f"Discovery queue is full - dropping file: {Path(file_path).name}")
    
    def _process_files_worker(self) -> None:
        """Worker thread to process files from queue"""
        log.info("Folder monitor worker thread started")
        
        # Track processed files to avoid duplicates
        processed_files: Set[str] = set()
        
        while self.running:
            try:
                # Get file from queue with timeout
                queue_item = self.processing_queue.get(timeout=1.0)
                
                if queue_item is None:  # Shutdown signal
                    break
                
                file_path, source_folder = queue_item
                
                # Process the file
                self._process_single_file(file_path, source_folder, processed_files)
                self.processing_queue.task_done()
                
            except queue.Empty:
                continue  # Timeout - check if still running
            except Exception as e:
                log.error(f"Error in folder monitor worker thread: {e}")
        
        log.info("Folder monitor worker thread stopped")
    
    def _process_single_file(self, file_path: str, source_folder: str, processed_files: Set[str]) -> None:
        """Process a single file by notifying callbacks
        
        Args:
            file_path: Path to the file
            source_folder: Path to the folder where the file was discovered
            processed_files: Set of already processed files to avoid duplicates
        """
        file_path_obj = Path(file_path)
        
        try:
            # Wait for file to be fully written (debounce)
            debounce_delay = ud_config.get_value(
                UpdateDataKeys.FolderMonitoring.DEBOUNCE_DELAY, 
                default=2.0
            )
            time.sleep(debounce_delay)
            
            # Check if file still exists
            if not file_path_obj.exists():
                log.warning(f"File no longer exists: {file_path_obj.name}")
                return
            
            # Check if file has already been processed
            if file_path in processed_files:
                log.debug(f"File already processed, skipping: {file_path_obj.name}")
                return
            
            # Add to processed files set
            processed_files.add(file_path)
            
            # Limit the size of processed_files set to avoid memory issues
            if len(processed_files) > 1000:
                # Remove oldest entries (approximation)
                to_remove = len(processed_files) - 900
                for _ in range(to_remove):
                    processed_files.pop()
            
            # Notify callbacks
            log.info(f"File discovered: {file_path_obj.name} in {source_folder}")
            for callback in self.callbacks:
                try:
                    callback(file_path, source_folder)
                except Exception as e:
                    log.error(f"Error in file discovery callback: {e}")
                
        except Exception as e:
            log.error(f"Error discovering file {file_path_obj.name}: {e}")


# Global instance
folder_monitor_service = FolderMonitorService()
