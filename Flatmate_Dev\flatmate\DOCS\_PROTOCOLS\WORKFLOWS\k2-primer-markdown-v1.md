# Protocol: K2 Sequential Reasoning Primer (Markdown Version)

**Version**: 1.0  
**Status**: Active  
**Author**: Cascade & Quinn  

---

## 1. Objective

To enforce a methodical, sequential reasoning process that prevents rushing to conclusions and ensures thorough problem-solving. This protocol is designed to counteract the tendency to skip steps in reasoning or jump to implementation without proper analysis.

## 2. Core Principles

1. **Sequential Thought**: Break down complex problems into clear, logical steps.
2. **Evidence-Based Reasoning**: Every conclusion must be supported by specific evidence.
3. **Explicit State Tracking**: Maintain awareness of what is known, unknown, and assumed.
4. **Hypothesis Testing**: Test assumptions before accepting them as facts.
5. **Completeness Check**: Verify all aspects of a problem have been addressed.

## 3. Reasoning Framework

### Step 3.1: Problem Definition

Before attempting to solve any problem:

* **Explicit Statement**: Clearly articulate what problem needs solving.
* **Scope Definition**: Define the boundaries of the problem space.
* **Success Criteria**: Establish how to determine when the problem is solved.

```
PROBLEM STATEMENT:
[Concise description of the problem]

SCOPE:
- [What's included]
- [What's excluded]

SUCCESS CRITERIA:
- [Measurable outcomes]
```

### Step 3.2: Knowledge Inventory

Take stock of what is known and unknown:

* **Known Facts**: List verified information relevant to the problem.
* **Knowledge Gaps**: Identify missing information critical to solving the problem.
* **Assumptions**: Explicitly state any assumptions being made.

```
KNOWN FACTS:
- [Fact 1]
- [Fact 2]

KNOWLEDGE GAPS:
- [Gap 1]
- [Gap 2]

ASSUMPTIONS:
- [Assumption 1]
- [Assumption 2]
```

### Step 3.3: Investigation Plan

Create a structured approach to fill knowledge gaps:

* **Research Questions**: Formulate specific questions to answer.
* **Information Sources**: Identify where to find needed information.
* **Investigation Methods**: Decide how to extract relevant information.

```
RESEARCH QUESTIONS:
1. [Question 1]
2. [Question 2]

INFORMATION SOURCES:
- [Source 1]
- [Source 2]

INVESTIGATION METHODS:
- [Method 1]
- [Method 2]
```

### Step 3.4: Sequential Analysis

Process information in a structured manner:

* **Step-by-Step Reasoning**: Document each logical step in the analysis.
* **Evidence Linking**: Connect each conclusion to specific evidence.
* **Alternative Explanations**: Consider multiple interpretations of the evidence.

```
REASONING CHAIN:
1. [Initial observation/fact]
2. [Logical step based on #1]
3. [Logical step based on #2]
...

EVIDENCE LINKS:
- Conclusion X is supported by [specific evidence]
- Conclusion Y is supported by [specific evidence]

ALTERNATIVE INTERPRETATIONS:
- [Alternative view 1]
- [Alternative view 2]
```

### Step 3.5: Solution Formulation

Develop solutions based on the analysis:

* **Solution Options**: Generate multiple possible solutions.
* **Evaluation Criteria**: Define how to assess solution quality.
* **Trade-off Analysis**: Compare solutions against criteria.

```
SOLUTION OPTIONS:
1. [Option 1]
2. [Option 2]

EVALUATION CRITERIA:
- [Criterion 1]
- [Criterion 2]

TRADE-OFF ANALYSIS:
- Option 1: [Strengths] / [Weaknesses]
- Option 2: [Strengths] / [Weaknesses]
```

### Step 3.6: Implementation Planning

Plan the execution of the chosen solution:

* **Action Steps**: Break down implementation into discrete tasks.
* **Dependencies**: Identify prerequisites for each step.
* **Validation Points**: Define checkpoints to verify progress.

```
ACTION STEPS:
1. [Step 1]
2. [Step 2]

DEPENDENCIES:
- Step 2 requires completion of Step 1
- Step 3 requires [external resource]

VALIDATION POINTS:
- After Step 1: [Expected outcome]
- After Step 2: [Expected outcome]
```

### Step 3.7: Completeness Verification

Before finalizing:

* **Requirement Check**: Verify all requirements have been addressed.
* **Edge Case Analysis**: Consider boundary conditions and exceptions.
* **Assumption Validation**: Confirm or update initial assumptions.

```
REQUIREMENTS COVERAGE:
- [Requirement 1]: [How addressed]
- [Requirement 2]: [How addressed]

EDGE CASES CONSIDERED:
- [Edge case 1]: [How handled]
- [Edge case 2]: [How handled]

ASSUMPTION UPDATES:
- [Initial assumption 1] → [Current status]
- [Initial assumption 2] → [Current status]
```

## 4. Implementation Guidelines

1. **Template Usage**: Use the provided templates for each reasoning step.
2. **Explicit Transitions**: Clearly mark transitions between reasoning stages.
3. **Reasoning Artifacts**: Preserve intermediate reasoning products.
4. **Backtracking**: When new information invalidates previous conclusions, explicitly update the reasoning chain.
5. **Meta-Cognition**: Periodically assess the quality of the reasoning process itself.

## 5. Application Contexts

This protocol should be applied in these situations:

* Complex problem-solving requiring multiple logical steps
* Debugging issues with unclear root causes
* Design decisions with significant architectural impact
* Risk assessment and mitigation planning
* Performance optimization tasks

---

## Usage Example

```
PROBLEM STATEMENT:
The application crashes when processing large CSV files.

SCOPE:
- CSV processing module
- Memory management
- Not addressing UI responsiveness

SUCCESS CRITERIA:
- Process 10MB CSV files without crashes
- Memory usage stays below 500MB
```

REASONING CHAIN:
1. The application crashes when processing large CSV files, suggesting a resource limitation issue.
2. Resource limitations could be memory-related (heap overflow), CPU-related (processing bottleneck), or I/O-related (file handling issues).
3. Looking at the error logs, I see 'OutOfMemoryError' exceptions occurring when the file size exceeds 5MB, pointing to memory as the primary constraint.
4. The current implementation loads the entire CSV into memory at once using pandas.read_csv() without any chunking mechanism.
5. [REVISION to #4] After reviewing the code more carefully, I see there is actually a chunking mechanism in place, but the chunk size is hardcoded to a value that's too large (1,000,000 rows).
6. Solution: Modify the CSV processing module to use a smaller, configurable chunk size based on available system memory and file size.

[Continue with remaining steps from the framework...]
