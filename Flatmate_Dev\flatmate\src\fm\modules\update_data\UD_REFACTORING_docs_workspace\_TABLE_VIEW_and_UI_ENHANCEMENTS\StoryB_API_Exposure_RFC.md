# Story B — Update Data: Directory Structure and API Exposure RFC (Phase 3.1)

Status: Draft  
Owner: Scrum Master (Bob)  
Source PRD: [PRD_table_view_and_ui_enhancements.md](PRD_table_view_and_ui_enhancements.md)

## Story

As a maintainer of the Update Data module, I need a clear, sanctioned API exposure pattern and consistent directory structure so that imports are predictable, refactoring risk is reduced, and new contributors can navigate and extend the module with less cognitive load.

## Problem Statement

The current folder naming and API exposure diverge from broader app norms. The entry-point/API is colocated with components, which causes import ambiguity, accidental deep imports into component internals, and inconsistent public surface management.

(Adapted from PRD lines 55–57)

## Options (RFC Scope)

- Option A: Introduce a module-level `api/` folder that re-exports the public surface with explicit symbols.  
- Option B: Keep colocation structure, but provide a root `__init__.py` that defines a clear `__all__` for sanctioned imports (and re-exports from internal modules).

The RFC will evaluate both, provide rationale, and propose a decision.

(Adapted from PRD lines 58–63)

## Goals

- Establish a single, documented import path for consumers of Update Data components and utilities.
- Reduce accidental deep imports and clarify which modules are stable/public vs internal.
- Make future refactors safer by centralizing the public surface in either `api/` or a well-defined package `__all__`.

## Out of Scope

- Functional changes to behavior at runtime.
- Renaming public symbols beyond what is required to present a clean surface (prefer re-exports over renames).
- Cross-module architectural changes outside the Update Data module.

## Acceptance Criteria

1) RFC document is written, reviewed, and approved within UD_REFACTORING docs.  
2) A single option (A or B) is selected with rationale, migration notes, and examples.  
3) Implementation applies zero functional changes: only import paths and public surface are updated.  
4) All intended consumers import from the sanctioned API path (update examples/guides accordingly).  
5) Update-Data-Module-Guide references the new API exposure pattern.

(Adapted from PRD lines 65–69)

## Deliverables

- RFC document (commit into UD_REFACTORING docs workspace).
- Implementation PR:
  - If Option A: Add `api/` with explicit re-exports; update consumer imports.
  - If Option B: Add/extend root `__init__.py` with explicit `__all__`; update consumer imports.
- Documentation updates:
  - Update-Data-Module-Guide (or equivalent) to show sanctioned import paths and examples.
- Migration notes:
  - Steps for replacing deep/internal imports with sanctioned paths.
  - Risks and backout plan (if any).

## Decision Drivers (to capture in RFC)

- Developer ergonomics and discoverability.
- Minimizing refactor friction and churn.
- Consistency with broader app/module patterns.
- Testability: ease of mocking and stubbing public surfaces.
- Long-term maintainability and clarity of boundaries.

## Testing & Validation

Commands (run from repo root):

- Git Bash:
  ./.venv_fm313/Scripts/python.exe -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Windows CMD/PowerShell:
  .venv_fm313\Scripts\python.exe -m pytest -q tests\gui\update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Optional headless:
  Add --offscreen (supported in conftest)

Validation:
- No functional regressions in Update Data GUI tests.
- Imports across the codebase continue to resolve.
- Lint/static checks pass for import paths (if configured).
- Documentation examples import from the sanctioned surface and run.

## Risks & Mitigations

- Risk: Hidden deep imports missed during migration.
  - Mitigation: Search for internal path patterns; add a pre-commit or CI check for disallowed import prefixes.
- Risk: Future drift between internal structure and public surface.
  - Mitigation: Treat `api/` or `__all__` as the single source of truth; document policy and add review checklist items.

## Tasks / Subtasks

- Draft RFC (problem, options, decision drivers, recommendation, migration, examples).
- Review RFC and record decision (A or B).
- Implement public surface:
  - If A: Create `api/` with explicit re-exports mapping to stable internals.
  - If B: Define `__all__` at module root and re-export stable internals.
- Update consumer imports to sanctioned path.
- Update module guide and examples.
- Run validations (tests/lints) and finalize.

## Dev Notes

- Prefer non-invasive re-exports over internal file churn.
- Consider adding a simple static check (optional) that flags imports bypassing the sanctioned surface.
- Document how to extend the public surface safely (review checklist, versioning expectations if applicable).