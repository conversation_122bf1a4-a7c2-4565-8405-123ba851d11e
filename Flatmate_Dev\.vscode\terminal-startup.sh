#!/bin/bash
# Terminal startup script for Flatmate Dev environment

# Navigate to project root
cd /c/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev

# Activate virtual environment if it exists
if [ -f "flatmate/.venv_fm313/Scripts/activate" ]; then
    source flatmate/.venv_fm313/Scripts/activate
fi

# Set any environment variables if needed
# export VARIABLE_NAME=value

# Print welcome message
echo "Flatmate Dev environment ready in $(pwd)"

# Keep the shell open
# exec bash
