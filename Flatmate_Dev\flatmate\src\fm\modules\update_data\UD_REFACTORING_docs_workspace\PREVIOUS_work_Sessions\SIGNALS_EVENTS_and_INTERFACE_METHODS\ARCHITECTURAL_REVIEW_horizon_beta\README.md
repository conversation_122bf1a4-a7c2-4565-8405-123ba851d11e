# Update Data Signals-Interfaces-Events Architectural Review (horizon_beta)

Scope: Audit the Update Data module’s signals, interface methods, and events to eliminate double-handling and migrate all GUI signal origination to the View layer (away from layout managers), with a clean Presenter↔View interface.

Status: Initial review captured from ground-truth code and existing refactor docs.

Related references:
- Guide: [Update-Data-Module-Guide.md](../../../../../DOCS/_GUIDES/Update-Data-Module-Guide.md)
- Presenter: [ud_presenter.py](../../../../update_data/ud_presenter.py)
- View: [\_ui/ud_view.py](../../_ui/ud_view.py)
- Interface: [\_ui/interface/i_view_interface.py](../../_ui/interface/i_view_interface.py)
- Managers:
  - [file_management.py](../../_ui/_presenter/file_management.py)
  - [file_list_manager.py](../../_ui/_presenter/file_list_manager.py)
  - [processing_manager.py](../../_ui/_presenter/processing_manager.py)
- Local bus: [local_event_bus.py](../../services/local_event_bus.py)
- Panels:
  - [center_panel_layout.py](../../_ui/_view/center_panel_layout.py)
  - [left_panel_layout.py](../../_ui/_view/left_panel_layout.py)
  - [guide_pane.py](../../_ui/_view/center_panel_components/guide_pane.py)
- Event dataclasses: [ui_events.py](../../_ui/ui_events.py)
- Refactor workspace notes:
  - [signal_refactor.md](../signal_refactor.md)
  - [widget_signal_refactoring_plan.md](../widget_signal_refactoring_plan.md)
  - [widget_signal_refactoring_plan.checklist.md](../widget_signal_refactoring_plan.checklist.md)