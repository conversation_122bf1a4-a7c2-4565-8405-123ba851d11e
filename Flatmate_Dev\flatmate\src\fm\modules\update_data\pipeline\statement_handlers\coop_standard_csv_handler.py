"""Co-operative Bank standard CSV format mapping."""


from ._base_statement_handler import *
from fm.core.data_services.standards.columns import Columns


class CoopStandardCSVHandler(StatementHandler):
    """Co-operative Bank standard CSV format mapping."""
    
    statement_type = StatementType(
        bank_name="Co-operative Bank",
        variant="standard",
        file_type="csv"
    )
    
    columns = ColumnAttributes(
        has_col_names=True,
        colnames_in_header=True,
        n_source_cols=4,
        data_start_row=1,
        date_format="%d/%m/%Y",
        source_col_names=(
            "Date", 
            "Details", 
            "Amount", 
            "Balance"
        ),
        target_col_names=(
            Columns.DATE,
            Columns.DETAILS,
            Columns.AMOUNT,
            Columns.BALANCE,
        ),
        has_account_column=False,
    )
    
    account = AccountNumberAttributes(
        pattern=r"02-\d{4}-\d{7}-\d{3}",
        in_file_name=True,
        location=(0, 0),  # Ensure we're looking at the full filename
        
    )
    
    metadata = SourceMetadataAttributes(
        has_metadata_rows=False,
    )
