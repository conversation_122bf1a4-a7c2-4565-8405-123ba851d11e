# Statement Handler System

This directory contains the core logic for parsing and standardizing various bank statement formats. The system is built on a base-class/sub-class architecture to promote code reuse and simplify the process of adding support for new statement types.

## Architecture Overview

1.  **`_base_statement_handler.py` (The "Engine"):**
    *   This is the abstract base class that contains all the shared logic for processing statement files.
    *   It defines a standardized processing pipeline (`_format_df`) that cleans, formats, and unifies DataFrames.
    *   It provides a public API (`process_file`) for external modules to use.

2.  **Concrete Handlers (e.g., `kiwibank_basic_csv_handler.py`):**
    *   These are the specific implementations for each bank/statement format.
    *   They inherit from `StatementHandler` and primarily serve to *describe* the format of the file they handle by configuring class attributes (e.g., `columns`, `account`, `metadata`).
    *   They should contain minimal logic, only overriding base methods (`_custom_format`) when absolutely necessary for bank-specific quirks.

---

## The Formatting Pipeline (`_format_df`)

The `_format_df` method is the heart of the base handler. It transforms a raw DataFrame read from a CSV into a standardized format. The pipeline is designed to be robust and clear, unifying different formats as early as possible.

**Pipeline Steps:**

1.  **Account Number Extraction:**
    *   The `_extract_account_number` method is called first. It operates on the raw DataFrame to find the account number in the file's metadata, data columns, or filename *before* any rows are sliced away.

2.  **Metadata Slicing:**
    *   The `data_start_row` attribute is used to slice off any header or metadata rows from the top of the DataFrame. This ensures the first row is the data header (if present) or the first data row.

3.  **Column Unification:**
    *   This is the core unification step. It inspects the `colnames_in_header` flag.
    *   If `True`, it renames columns based on the `source_col_names` to `target_col_names` mapping.
    *   If `False`, it directly assigns `target_col_names` as the column headers.
    *   After this step, all DataFrames have the same standardized column names.

4.  **Data Cleaning & Header Removal:**
    *   Fully empty rows are dropped.
    *   A robust check is performed to remove any lingering header row by finding and dropping rows where the 'Date' column does not contain a valid date.

5.  **Standardization & Enrichment:**
    *   The `Source Filename` and `Account` columns are added.
    *   The `_standardize_dates` method is called, using the handler-specific `date_format` to ensure efficient and accurate parsing.
    *   A `Details` column is created if one doesn't exist by concatenating other common descriptive columns.
    *   The columns are reordered according to the `StandardColumns` enum for consistency.

---

## How to Add a New Handler

1.  Create a new file (e.g., `my_bank_handler.py`) in this directory.
2.  Create a class that inherits from `StatementHandler`.
3.  Define the `columns`, `account`, and `metadata` attributes by instantiating `ColumnAttributes`, `AccountNumberAttributes`, and `MetadataAttributes` with the specific values for the new statement format.
4.  If the statement has any unique formatting needs not covered by the base pipeline, override the `_custom_format` method.
5.  Add the new handler to the `ALL_HANDLERS` list in `statement_handler_factory.py`.

---

## Changelog (July 2025)

- **Major Refactoring:** Refactored the `_format_df` pipeline to be more robust and unified. Introduced the `data_start_row` attribute to cleanly handle metadata slicing.
- **Account Number Logic:** Fixed the `_extract_account_number` method to correctly use handler attributes and a clear `if/elif` precedence for extraction locations (metadata, data, filename).
- **Date Parsing:** Corrected date parsing logic to use the handler-specific `date_format` throughout the pipeline, eliminating pandas warnings and improving efficiency.
- **API Refinement:** Introduced `process_file` as the primary public API, encapsulating the file reading and formatting process within the handler itself. The `can_handle_file` method was added for quick file identification.
