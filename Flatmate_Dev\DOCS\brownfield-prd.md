# Brownfield PRD — Update Data Module (Event-First Refactor)

Status: Active  
Owner: PM/PO: Update Data Initiative  
Scope: Update Data module event-first architecture and targeted UI quality improvements

Summary
The Update Data module has been refactored toward an event-first UI architecture. This PRD defines the scope, objectives, and acceptance criteria for the refactor and subsequent focused improvements. Toolbar improvement work is deferred to a dedicated plan and will not be part of this phase.

Goals
- Maintain a strict separation of concerns via an event-first architecture
- Ensure dialog creation is owned by the View, invoked via *_DIALOG_REQUESTED events
- Improve the reliability and clarity of Update Data UI behaviors with test-gated changes

Out of Scope
- Full visual redesigns
- Broad UI rewrites beyond the Update Data module
- Toolbar redesign (addressed in a separate plan)

Key References
- Deep Dive: [flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Event Contracts (Authoritative): [flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md](flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md)
- Testing Protocol: [flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md)
- Phase 3 Plan (Focused): [DOCS/Table-View-Improvement-Plan_v0.md](DOCS/Table-View-Improvement-Plan_v0.md)

Architecture Principles
- Event-first UI: Views translate Qt signals to local intents and render from typed state events
- Channel separation: Left panel SOURCE_SELECT_REQUESTED vs File Pane add_files_requested
- Dialog ownership: View subscribes to *_DIALOG_REQUESTED; presenters/managers emit DialogRequestEvent
- Presenter/event boundaries: Runtime flows use events; presenters do not render dialogs directly

Phase 1 — Completed
- Authoritative UI event contracts created and enforced
- Dialog-request policy implemented (View ownership)
- Initial contract tests and testing protocol guide added
- PRD refocused to Update Data module

Phase 2 — Completed (Extended)
- Pytest scaffold with optional offscreen mode
- FileSelector API guide and cross-links
- Smoke tests guide and Test Taxonomy integrated
- New tests added:
  - tests/gui/update_data/test_harden_contracts.py
  - tests/gui/update_data/test_smoke.py
  - tests/gui/update_data/test_view_dialog_subscription.py (view-like offscreen subscription)

Phase 3 — In Progress
Title: Phase 3: Table View Improvement Plan v0 + Test-Gated Quality Pass  
Location: [DOCS/Table-View-Improvement-Plan_v0.md](DOCS/Table-View-Improvement-Plan_v0.md)

Planned Deliverables (v0)
- Task A: Consistent refresh on FileListUpdatedEvent with correct row count
  (Clarification: "row count" means the number of visible, data-backed rows in the Table View after a FileListUpdatedEvent is processed. If the event payload includes N files, the Table View must render exactly N rows, no more, no less.)
- Task B: Clear and/or restore selection state appropriately on data rebind
  (Clarification: "data rebind" refers to the Table View re-attaching or re-synchronizing its model/view to a new or updated underlying dataset after a FileListUpdatedEvent. During a rebind, the selection model must be cleared for removed items and may optionally restore selection for surviving items by stable identifier e.g., file path.)
 
- Integrated test execution (focused suite minimum) using Flatmate venv

Success Criteria
- Acceptance criteria for Task A and Task B satisfied
- Tests pass locally and are suitable for CI
- Documentation remains consistent with contracts and deep-dive

Run Tests (Windows, Flatmate venv)
- Focused run:
  - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Full module tests (if stable):
  - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data --maxfail=1 --disable-warnings
- Offscreen mode (if needed):
  - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data --offscreen --maxfail=1 --disable-warnings

Notes
- Evidence is inline-by-default; use EVIDENCE/ directory for heavy troubleshooting sessions
- Any policy changes discovered during Phase 3 must be reflected in ui_event_contracts.md and cross-linked guides

Aesthetics & UX Focus (Phase 3.1 preview; influences v0 acceptance where low-risk)
- Goal: Match or improve upon the legacy Table View aesthetics while preserving clarity and performance.
- Targets:
  - Column resizing with sensible defaults and persistence (where appropriate)
  - Sensible layout and spacing for readability (row height, padding, header styling)
  - Show/hide columns affordance and consistent autosizing rules
  - Visual states: selection/highlight consistency, hover cues (if supported), and error/empty states
- Constraint: Limit v0 to correctness and low-risk tweaks; defer larger styling work to Phase 3.1 with explicit acceptance criteria and before/after evidence.

Directory Structure & API Exposure (Action item to schedule)
- Concern: Current folder structure and naming conventions are confusing; entry-point/API is colocated with components, diverging from app-wide pattern of dedicated API exposure folders.
- Plan:
  - Propose a small RFC with 1–2 concrete options:
    1) Introduce an explicit api/ entry point folder that re-exports the public surface
    2) Maintain component colocation but add a thin __init__.py at module root that defines the sanctioned API (explicit __all__)
  - Acceptance: RFC approved and applied with zero functional change, only import path cleanup and doc updates. Cross-reference in Update-Data-Module-Guide.


>> an important focus for the table view is the aesthetic quality 
I would like to at least match, and hopefully imprve upon the old table view aesthetics
and improve the ux (resizable collumns sensible layout show hide columns etc )
>> I'm also a little concerned about the dir structure and naming conventions, I think this is confusing fo developers, generally in the app we haver had a habit of exposing the api 
with all components in a dediacted folder - currenlty the entry point/api is in the folder with the components 
