# 06 — Widget Catalog (Representative, App-Wide)

Purpose
Catalog representative widgets/components, their styling hooks (objectName, properties), and expected stylesheet selectors. Based on inspected files and established patterns.

Note
Direct global search for hooks returned no results due to regex/tool limitations; this catalog is compiled from reviewed code and known patterns in this repo.

A) Core Style Sources
- Theme (variable-driven): [theme.qss](flatmate/src/fm/gui/styles/theme.qss)
  - Imports palette: `@import url("palette.qss");`
  - Selectors include:
    - QWidget, #left_panel, #right_panel, #right_side_bar, #content_area
    - QPushButton[type="primary"|"secondary"]
    - QFrame#TableViewToolbar
    - #file_tree (and nested QHeaderView::section, ::item, :selected)
- Legacy/Global: [style.qss](flatmate/src/fm/gui/styles/style.qss)
  - Broad selectors: QWidget, QTableView, QTextEdit, QHeaderView::section, QScrollArea, QScrollBar, etc.
  - Hard-coded colors (not palette variables)

B) Update Data — File View (UDFileView)
1) Container Pane
- Class: UDFileView (inherits BasePane)
- File: [ud_file_view.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/ud_file_view.py)
- Layout: QVBoxLayout with tight margins (0,0,0,0), spacing 2
- objectName usages:
  - self.setObjectName("FileTreePane") — used to scope possible pane-specific styles (not explicitly in theme.qss today)
- Styling hooks:
  - Relies on sub-component hooks (file_tree) and button properties (type)

2) File Tree
- Class: FileTree (QTreeWidget)
- File: [components/file_tree.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/components/file_tree.py)
- objectName: `setObjectName("file_tree")`
- Features: alternating rows, header context menu
- Theme selectors expected to apply:
  - #file_tree (container)
  - #file_tree::item, #file_tree::item:alternate, #file_tree::item:selected
  - #file_tree QHeaderView::section

3) Add/Remove Buttons Bar
- File: [ud_file_view.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/ud_file_view.py)
- Current container: QWidget with objectName "FileTreeButtonBar" (recommend QFrame#TableViewToolbar per RFC)
- Buttons:
  - add_btn.setProperty("type", "primary")
  - remove_btn.setProperty("type", "secondary")
  - Unpolish/polish to apply style
- Theme selectors expected:
  - QPushButton[type="primary"]
  - QPushButton[type="secondary"]
  - If toolbar container adjusted: QFrame#TableViewToolbar QPushButton { ... }

4) Context Menu and Signals
- Context menu styling defaults (Qt); theme not currently customizing QMenu here
- Interaction wiring adheres to event-first architecture (not styling-related)

C) Legacy File Pane (Reference)
- File: [old_files_backup/FILE_PANE/widgets/file_browser.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/old_files_backup/FILE_PANE/widgets/file_browser.py)
- Composition: QTreeWidget + PanelActionButton (with ButtonType enums)
- Styling: Historically likely relied on different button class styling and possibly global rules

D) Shared/Global Shell Widgets (Selectors in theme.qss)
- Panels
  - #left_panel, #right_panel, #right_side_bar, #content_area — background and sizing behaviors
- Buttons
  - QPushButton[type="primary"|"secondary"] — color, radius, hover/pressed
- Table/Tree Headers
  - QHeaderView::section under #file_tree
- Scrollbars (legacy coverage in style.qss)
  - QScrollBar rules with hard-coded colors
- Text/Labels (legacy in style.qss)
  - QLabel, QTextEdit, ComboBox, CheckBox, etc., with explicit colors and sizes

E) Known/Implied Hooks and Conventions
- Use objectName for scoping theme: e.g., #file_tree
- Use property-based styles for portable button theming: QPushButton[type="..."]
- Use QFrame with explicit objectName for group containers (e.g., #TableViewToolbar)
- Avoid inline setStyleSheet on widgets in refactored modules to prevent override conflicts

F) Gaps Identified
- Toolbar container in UD File View does not match theme hook (uses QWidget#"FileTreeButtonBar" vs QFrame#TableViewToolbar)
- theme.qss toolbar button rules are commented; need minimal reinstatement
- style.qss contains global rules that may conflict if load order or specificity is not controlled

G) Candidate Additional Areas (Next Census Iteration)
- Shared table view v2 components under fm/gui/_shared_components/table_view_v2
- Toolbars in shared components under fm/gui/_shared_components/table_view_v2/components/toolbars
- Main window and central panels under fm/gui/_main_window_components

Conclusion
This catalog captures the key widgets and their styling integration points driving the UD File View appearance and related shell elements. It highlights where theme.qss is intended to apply versus legacy style.qss globals and establishes the hooks to align in the minimal RFC.