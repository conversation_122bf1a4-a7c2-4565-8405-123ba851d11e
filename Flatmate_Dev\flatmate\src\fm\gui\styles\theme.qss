/* Import color palette */
@import url("palette.qss");

/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;  /* Base font size */
    /* Keep global minimal to avoid breaking widgets */
}

QWidget {
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}

/* Main panels */
#left_panel, #right_panel {
    background-color: var(--color-nav-bg);
    min-width: 150px;
}

/* Right side bar - no min-width to conform to content */
#right_side_bar {
    background-color: var(--color-nav-bg);
}

#left_panel QLabel {
    background: transparent;
}

#content_area {
    background-color: var(--color-bg-dark);
}

/* Primary Action Buttons */
QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-weight: bold;
}

QPushButton[type="primary"]:hover {
    background-color: var(--color-primary-hover);
}

QPushButton[type="primary"]:pressed {
    background-color: var(--color-primary-pressed);
}

/* Secondary Buttons */
QPushButton[type="secondary"] {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="secondary"]:hover {
    background-color: var(--color-secondary-hover);
}

QPushButton[type="secondary"]:pressed {
    background-color: var(--color-secondary-pressed);
}

/* File Tree */
#file_tree {
    border: 1px solid var(--color-border);
    border-radius: 4px;
    background-color: var(--color-file-display-bg);
    color: var(--color-text-primary);
}

#file_tree::item {
    padding: 4px;
    border-bottom: 1px solid var(--color-border);
    background: transparent; /* ensure items inherit dark background from container */
}

#file_tree::item:selected {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
}
#file_tree::item:alternate {
    background-color: #242424; /* subtle alternate for dark theme */
}

#file_tree::item:hover {
    background-color: var(--color-panel-bg);
}

#file_tree QHeaderView::section {
    background-color: var(--color-panel-bg);
    color: var(--color-text-primary);
    padding: 4px;
    border: none;
    border-right: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
}
#file_tree QHeaderView {
    background-color: var(--color-panel-bg);
}

/* Table View Toolbar Styling - TESTING BASIC BORDER */
QFrame#TableViewToolbar {
    border: 1px solid #2A5A3A;  /* Very dark, muted green */
    border-radius: 4px;         /* Match file tree container radius */
}

/* Tool Groups - subtle separators - COMMENTED OUT FOR DEBUGGING */
/*
QWidget#FilterGroup {
    background-color: transparent;
    padding: 4px 8px;
    border-right: 1px solid var(--color-border);
    margin-right: 4px;
}

QWidget#ColumnGroup {
    background-color: transparent;
    padding: 4px 8px;
    border-right: 1px solid var(--color-border);
    margin-right: 4px;
}

QWidget#ExportGroup {
    background-color: transparent;
    padding: 4px 8px;
}
*/

/* Toolbar buttons */
QFrame#TableViewToolbar QPushButton {
    background-color: var(--color-bg-dark);
    border: 1px solid var(--color-border);
    border-radius: 3px;
    padding: 4px 8px;
    color: var(--color-text-primary);
    min-height: 20px;
}

QFrame#TableViewToolbar QPushButton:hover {
    background-color: var(--color-secondary);
    border: 1px solid var(--color-primary);
    color: var(--color-text-primary);
}

QFrame#TableViewToolbar QPushButton:pressed {
    background-color: var(--color-secondary-pressed);
    border: 1px solid var(--color-primary);
    color: var(--color-text-primary);
}

/* Toolbar buttons with specific types - higher specificity */
QFrame#TableViewToolbar QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-primary);
}

QFrame#TableViewToolbar QPushButton[type="primary"]:hover {
    background-color: var(--color-primary-hover);
    border: 1px solid var(--color-primary-hover);
}

QFrame#TableViewToolbar QPushButton[type="secondary"] {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-secondary);
}

QFrame#TableViewToolbar QPushButton[type="secondary"]:hover {
    background-color: var(--color-secondary-hover);
    border: 1px solid var(--color-secondary-hover);
}

/* Toolbar inputs - COMMENTED OUT FOR DEBUGGING */
/*
QFrame#TableViewToolbar QLineEdit {
    background-color: var(--color-bg-dark);
    border: 1px solid var(--color-border);
    border-radius: 3px;
    padding: 4px;
    color: var(--color-text-primary);
    min-height: 20px;
}

QFrame#TableViewToolbar QLineEdit:focus {
    border: 1px solid var(--color-primary);
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}

QFrame#TableViewToolbar QComboBox {
    background-color: var(--color-bg-dark);
    border: 1px solid var(--color-border);
    border-radius: 3px;
    padding: 4px;
    color: var(--color-text-primary);
    min-height: 20px;
    min-width: 100px;
}

QFrame#TableViewToolbar QComboBox:hover {
    border: 1px solid var(--color-primary);
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}

QFrame#TableViewToolbar QComboBox::drop-down {
    border: none;
    width: 20px;
    background-color: transparent;
}

QFrame#TableViewToolbar QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid var(--color-text-primary);
    margin-right: 4px;
}

QFrame#TableViewToolbar QLabel {
    background-color: transparent;
    color: var(--color-text-secondary);
    font-weight: 500;
}
*/
