# Protocol: K2 Sequential Reasoning Primer (Hybrid Version)

**Version**: 1.0  
**Status**: Active  
**Author**: Cascade & Quinn  

---

## 1. Objective

To combine the structured reasoning of the sequential-thinking tool with the documentation benefits of markdown. This hybrid approach allows for rigorous step-by-step analysis while maintaining readable, shareable documentation.

## 2. Core Principles

1. **Tool-Assisted Reasoning**: Use the sequential-thinking tool for complex reasoning steps
2. **Document-Centric Output**: Produce clear markdown documentation as the final product
3. **Explicit Reasoning Structure**: Make thinking steps visible and traceable
4. **Revision Transparency**: Clearly document when and why previous conclusions are revised
5. **Progressive Refinement**: Start with broad analysis and refine through sequential thinking

## 3. Hybrid Framework

### Step 3.1: Document Initialization

Begin by creating a markdown document that outlines the problem and approach:

```markdown
# Analysis: [Problem Title]

## Problem Statement
[Clear articulation of the problem]

## Scope
- [What's included]
- [What's excluded]

## Success Criteria
- [Measurable outcomes]
```

### Step 3.2: Sequential Thinking Sessions

For each major section of analysis, use the sequential-thinking tool to reason through the problem:

1. **Identify Analysis Node**: Determine a specific aspect of the problem to analyze
2. **Run Sequential Thinking**: Use the tool to explore this aspect thoroughly
3. **Document Results**: Capture the key insights in the markdown document

### Step 3.3: Markdown Integration

After each sequential thinking session, integrate the results into the markdown document:

```markdown
## Analysis Node: [Specific Aspect]

### Sequential Thinking Process
*The following represents a sequential thinking session focused on [specific aspect]*

#### Thought 1: Initial Observation
[Content of first thought]

#### Thought 2: Logical Development
[Content of second thought]

#### Thought 3: Further Analysis
[Content of third thought]

#### Thought 4: Revision of Thought 2
[Content explaining why thought 2 needed revision and the updated understanding]

### Key Insights
- [Insight 1]
- [Insight 2]
- [Insight 3]
```

## 4. Implementation Guide

### Step 4.1: Problem Decomposition

Break down the overall problem into distinct analysis nodes:

```markdown
# Analysis Nodes
1. [Node 1 - e.g., "Understanding Current System Behavior"]
2. [Node 2 - e.g., "Identifying Root Causes"]
3. [Node 3 - e.g., "Evaluating Solution Options"]
```

### Step 4.2: Sequential Thinking Template

For each analysis node, use this template with the sequential-thinking tool:

```
# Sequential Thinking for [Analysis Node]

thought: "[Initial observation or question about this aspect of the problem]"
thoughtNumber: 1
totalThoughts: 5  # Initial estimate
nextThoughtNeeded: true
isRevision: false

thought: "[Development based on first thought]"
thoughtNumber: 2
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

# Continue with additional thoughts...
```

### Step 4.3: Markdown Documentation Template

After completing sequential thinking, document the process and outcomes:

```markdown
## Analysis of [Node Name]

### Reasoning Process
*Sequential thinking summary:*

1. Started with observation that [initial thought]
2. Developed understanding that [second thought]
3. Realized that [third thought]
4. Revised earlier thinking about [point of revision] because [reason]
5. Concluded that [final thought]

### Evidence
- [Evidence point 1]
- [Evidence point 2]

### Conclusions
- [Key conclusion 1]
- [Key conclusion 2]
```

## 5. Practical Example

### Initial Document

```markdown
# Analysis: CSV Processing Performance Issues

## Problem Statement
The application crashes when processing large CSV files.

## Scope
- CSV processing module
- Memory management
- Not addressing UI responsiveness

## Success Criteria
- Process 10MB CSV files without crashes
- Memory usage stays below 500MB

## Analysis Nodes
1. Current System Behavior
2. Resource Constraints
3. Solution Options
```

### Sequential Thinking for Node 1

```
# Sequential Thinking for "Current System Behavior"

thought: "The application crashes when processing large CSV files. I need to understand the exact behavior and failure pattern."
thoughtNumber: 1
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

thought: "Looking at the logs, the crash happens during the pandas.read_csv() call, suggesting memory issues."
thoughtNumber: 2
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

thought: "The application seems to be loading the entire CSV into memory at once without chunking."
thoughtNumber: 3
totalThoughts: 5
nextThoughtNeeded: true
isRevision: false

thought: "Wait, reviewing the code more carefully, there is a chunking mechanism, but the chunk size is set too large (1,000,000 rows)."
thoughtNumber: 4
totalThoughts: 5
nextThoughtNeeded: true
isRevision: true
revisesThought: 3

thought: "The crash consistently occurs at approximately 5MB file size, regardless of row count, suggesting a hard memory limit rather than a row processing issue."
thoughtNumber: 5
totalThoughts: 5
nextThoughtNeeded: false
isRevision: false
```

### Markdown Documentation

```markdown
## Analysis of Current System Behavior

### Reasoning Process
*Sequential thinking summary:*

1. Started by examining the crash scenario with large CSV files
2. Identified that crashes occur during the pandas.read_csv() call
3. Initially thought the entire CSV was being loaded at once
4. Revised understanding: chunking exists but with excessive chunk size (1,000,000 rows)
5. Determined crashes occur at ~5MB file size regardless of row count

### Evidence
- Error logs showing OutOfMemoryError exceptions
- Code review revealing chunking implementation with 1,000,000 row setting
- Testing with various file sizes showing consistent 5MB failure threshold

### Conclusions
- The application has a memory constraint of approximately 5MB
- The existing chunking mechanism is ineffective due to excessive chunk size
- The issue is related to absolute file size rather than row count
```

## 6. Integration with Full Analysis

Continue this process for each analysis node, then integrate all nodes into a comprehensive document:

```markdown
# CSV Processing Performance Analysis

[Executive Summary]

## Problem Understanding
[Content from Node 1 analysis]

## Root Cause Analysis
[Content from Node 2 analysis]

## Solution Options
[Content from Node 3 analysis]

## Recommendations
[Synthesized recommendations based on all analysis nodes]
```

## 7. Benefits of Hybrid Approach

1. **Rigorous Thinking**: Sequential-thinking tool enforces disciplined reasoning
2. **Clear Documentation**: Markdown provides readable, shareable output
3. **Transparent Process**: Reasoning steps are preserved and visible
4. **Revisable Analysis**: Revisions are explicitly tracked and documented
5. **Modular Approach**: Complex problems are broken into manageable nodes

---

## Usage Notes

- Use sequential thinking for complex reasoning tasks where step-by-step logic is crucial
- Create markdown documentation for all projects requiring communication with stakeholders
- Preserve both the raw sequential thinking output and the refined markdown documentation
- For simpler tasks, the markdown-only approach may be sufficient
- For highly complex analysis, multiple sequential thinking sessions may be needed for a single node
