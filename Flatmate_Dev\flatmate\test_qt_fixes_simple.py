#!/usr/bin/env python3
"""
Simple test to verify Qt fixes without GUI.
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports_and_fixes():
    """Test that the Qt fixes don't break imports and basic functionality."""
    
    print("🧪 Testing Qt fixes...")
    
    try:
        # Test QSizePolicy import and usage
        from PySide6.QtWidgets import QSizePolicy, QTreeWidget, QFrame
        print("✓ PySide6 imports successful")
        
        # Test the enum usage that was fixed
        policy = QSizePolicy.Policy.Expanding
        print("✓ QSizePolicy.Policy.Expanding enum works")
        
        # Test QFrame creation (changed from QWidget)
        frame = QFrame()
        frame.setObjectName("TableViewToolbar")
        print("✓ QFrame with TableViewToolbar objectName works")
        
        # Test file view import
        from fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view import UDFileView
        from fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.config import FileConfig
        print("✓ UDFileView import successful")
        
        # Test config creation
        config = FileConfig.default()
        print("✓ FileConfig creation successful")
        
        print("\n🎯 All Qt fixes verified successfully!")
        print("\nChanges made:")
        print("  1. ✅ Button container: QWidget → QFrame")
        print("  2. ✅ Object name: 'FileViewFooter' → 'TableViewToolbar'")
        print("  3. ✅ Button types: 'select_btn' → 'primary'/'secondary'")
        print("  4. ✅ QSizePolicy: Fixed enum usage")
        print("  5. ✅ Theme.qss: Uncommented toolbar button styles")
        print("  6. ✅ Theme.qss: Added specific primary/secondary toolbar styles")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports_and_fixes()
    sys.exit(0 if success else 1)
