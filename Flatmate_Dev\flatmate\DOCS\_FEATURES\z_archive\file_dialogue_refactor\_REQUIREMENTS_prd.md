# Requirements: File Dialogue System Refactor

## 1. Overview

This document outlines the requirements for refactoring the file dialogue system. The primary goal is to address critical architectural issues, eliminate technical debt, and align the component with the project's established MVP (Model-View-Presenter) patterns.

## 2. Problem Statement

The current file dialogue system violates core architectural principles. The presenter layer is tightly coupled with the Qt view, directly accessing widgets and bypassing the prescribed view interface. This creates fragile code, makes testing difficult, and contradicts the "Zero Qt Coupling in Presenter" rule. The file naming and structure also do not follow project conventions.

## 3. Core Requirements

The refactored system **must** meet the following criteria:

| ID  | Requirement                                                               | Acceptance Criteria                                                                                                                              |
| --- | ------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1   | **Adhere to Clean MVP Pattern**                                           | The system is fully refactored into a clean Presenter and View, with all communication handled via a dedicated interface.                            |
| 2   | **Eliminate Qt Coupling in Presenter**                                    | The `presenter.py` file contains **zero** `PyQt` imports and does not reference any Qt-specific classes or methods.                                  |
| 3   | **Use View Interface for Operations**                                     | The presenter interacts with the view **only** through methods defined on the view's interface (e.g., `view.show_dialog()`).                      |
| 4   | **Implement Correct Naming Conventions**                                  | The file `get_files.py` is renamed to `file_selector.py`, and any associated class names are updated to reflect this change (e.g., `FileSelector`). |
| 5   | **Maintain Existing Functionality**                                       | The refactored dialogue system provides the same core functionality as the original: allowing the user to select files and/or folders.            |

## 4. Out of Scope

-   Adding new functionality to the file dialogue.
-   Changing the visual appearance of the dialogue.
