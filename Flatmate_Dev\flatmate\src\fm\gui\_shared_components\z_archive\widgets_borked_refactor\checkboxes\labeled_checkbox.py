"""
Labeled Checkbox Component

Enhanced labeled checkbox following App-Wide Widget Pattern with BaseWidget inheritance.
Maintains exact same API as original while adding configuration capabilities.
"""

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import QCheckBox, QHBoxLayout

from ..base.base_widget import BaseWidget
from ..config.widget_config import CheckBoxConfig


class LabeledCheckBox(BaseWidget):
    """Enhanced labeled checkbox following App-Wide Widget Pattern.
    
    A checkbox with a label in a horizontal layout.
    Maintains exact same API as original while adding:
    - Configuration system
    - Content management
    - Style loading
    - Runtime flexibility
    
    Signals:
        state_changed: Emitted when the checkbox state changes
    """
    
    # Checkbox-specific signals (maintain compatibility)
    state_changed = Signal(bool)
    
    def __init__(self, label_text="", checked=False, tooltip=None, parent=None):
        """Initialize the labeled checkbox.
        
        Args:
            label_text: Text to display next to the checkbox (maintains original API)
            checked: Initial state of the checkbox (maintains original API)
            tooltip: Optional tooltip text (maintains original API)
            parent: Parent widget
        """
        self._label_text = label_text
        self._checked = checked
        self._tooltip = tooltip
        super().__init__(parent)
    
    def _get_default_config(self) -> CheckBoxConfig:
        """Return default configuration for labeled checkboxes."""
        return CheckBoxConfig(
            checked=self._checked,
            label_text=self._label_text,
            tooltip=self._tooltip
        )
    
    def _setup_ui(self):
        """Initialize checkbox UI components."""
        # Main layout (maintain exact same layout as original)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Checkbox (maintain exact same structure)
        self.checkbox = QCheckBox()
        layout.addWidget(self.checkbox)
        layout.addStretch()
        
        # Connect signals (maintain exact same signal behavior)
        self.checkbox.stateChanged.connect(
            lambda state: self.state_changed.emit(state == Qt.CheckState.Checked)
        )
    
    def _apply_configuration(self):
        """Apply configuration to checkbox."""
        self.checkbox.setText(self._config.label_text)
        self.checkbox.setChecked(self._config.checked)
        self.checkbox.setEnabled(self._config.enabled)
        
        if self._config.tooltip:
            self.checkbox.setToolTip(self._config.tooltip)
        
        # Apply size constraints if configured
        if self._config.minimum_width:
            self.setMinimumWidth(self._config.minimum_width)
        if self._config.minimum_height:
            self.setMinimumHeight(self._config.minimum_height)
        if self._config.maximum_width:
            self.setMaximumWidth(self._config.maximum_width)
        if self._config.maximum_height:
            self.setMaximumHeight(self._config.maximum_height)
    
    def _apply_content(self):
        """Apply content to checkbox."""
        if self._content:
            self.checkbox.setText(str(self._content))
    
    # === CHECKBOX-SPECIFIC METHODS (Maintain original API) ===
    
    def is_checked(self) -> bool:
        """Get the current state of the checkbox (original API)."""
        return self.checkbox.isChecked()
    
    def set_checked(self, checked: bool):
        """Set the state of the checkbox (original API)."""
        self.checkbox.setChecked(checked)
        self._config.checked = checked
    
    def text(self) -> str:
        """Get checkbox label text."""
        return self.checkbox.text()
    
    def setText(self, text: str):
        """Set checkbox label text."""
        self.checkbox.setText(text)
        self._config.label_text = text
    
    def setEnabled(self, enabled: bool):
        """Set checkbox enabled state."""
        self.checkbox.setEnabled(enabled)
        self._config.enabled = enabled
    
    def isEnabled(self) -> bool:
        """Get checkbox enabled state."""
        return self.checkbox.isEnabled()
    
    def setToolTip(self, tooltip: str):
        """Set checkbox tooltip."""
        self.checkbox.setToolTip(tooltip)
        self._config.tooltip = tooltip
    
    def setChecked(self, checked: bool):
        """Set checkbox checked state (Qt compatibility)."""
        self.set_checked(checked)
    
    def isChecked(self) -> bool:
        """Get checkbox checked state (Qt compatibility)."""
        return self.is_checked()
    
    # === ENHANCED METHODS (New functionality) ===
    
    def set_label_text(self, text: str) -> 'LabeledCheckBox':
        """Set checkbox label text (chainable method)."""
        return self.configure(label_text=text)
    
    def set_checked_state(self, checked: bool) -> 'LabeledCheckBox':
        """Set checkbox checked state (chainable method)."""
        return self.configure(checked=checked)
    
    def set_label_position(self, position: str) -> 'LabeledCheckBox':
        """Set label position (chainable method)."""
        return self.configure(label_position=position)
